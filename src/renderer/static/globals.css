@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* radius */
  --chatbox-radius-none: 0rem;
  --chatbox-radius-xs: 0.125rem;
  --chatbox-radius-sm: 0.25rem;
  --chatbox-radius-md: 0.5rem;
  --chatbox-radius-lg: 1rem;
  --chatbox-radius-xl: 1.5rem;
  --chatbox-radius-xxl: 2rem;

  /* spacing */
  --chatbox-spacing-none: 0rem;
  --chatbox-spacing-3xs: 0.125rem;
  --chatbox-spacing-xxs: 0.25rem;
  --chatbox-spacing-xs: 0.5rem;
  --chatbox-spacing-sm: 0.75rem;
  --chatbox-spacing-md: 1rem;
  --chatbox-spacing-lg: 1.25rem;
  --chatbox-spacing-xl: 1.5rem;
  --chatbox-spacing-xxl: 2rem;
}

.mantine-Spotlight-actionsGroup {
  margin-bottom: 2rem;
}

.mantine-Spotlight-actionDescription {
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

/* KaTeX styles for preventing overflow */
.katex-display {
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 0.5rem;
}

.katex-display > .katex {
  max-width: 100%;
  display: inline-block;
}

/* Hide scrollbar for inline math */
.katex:not(.katex-display) {
  max-width: 100%;
  overflow: hidden;
}

/* Style scrollbar for displayed math equations */
.katex-display::-webkit-scrollbar {
  height: 6px;
}

.katex-display::-webkit-scrollbar-track {
  background: transparent;
}

.katex-display::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.katex-display:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark mode scrollbar */
[data-mantine-color-scheme="dark"] .katex-display::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-mantine-color-scheme="dark"] .katex-display:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.copilot-picker-scroll-area .mantine-ScrollArea-scrollbar {
  padding-left: var(--mantine-spacing-md);
  padding-right: var(--mantine-spacing-md);
}