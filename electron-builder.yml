productName: Chatbox
appId: xyz.chatboxapp.app
asar: true
asarUnpack: "**\\*.{node,dll}"
files:
  - dist
  - node_modules
  - package.json

afterSign: .erb/scripts/notarize.js

# releaseInfo:
#   releaseNotes: See the changelog for details

mac:
  notarize: false
  category: public.app-category.developer-tools
  target:
    target: default
    arch:
      - arm64
      - x64
  type: distribution
  hardenedRuntime: true
  entitlements: assets/entitlements.mac.plist
  entitlementsInherit: assets/entitlements.mac.plist
  gatekeeperAssess: false

dmg:
  contents:
    - x: 130
      y: 220
    - x: 410
      y: 220
      type: link
      path: /Applications

win:
  target:
    - target: nsis
      arch:
        - x64
        - arm64
  verifyUpdateCodeSignature: false
  artifactName: ${productName}-${version}-Setup.${ext}
  sign: ./custom_win_sign.js
  signingHashAlgorithms:
    - sha256

nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  include: assets/installer.nsh

linux:
  target:
    - target: AppImage
      arch:
        - x64
        - arm64
    - target: deb
      arch:
        - x64
        - arm64
  category: Development
  artifactName: ${productName}-${version}-${arch}.${ext}

directories:
  app: release/app
  buildResources: assets
  output: release/build

extraResources:
  - ./assets/**

publish:
  - provider: s3
    bucket: chatbox
    endpoint: https://********************************.r2.cloudflarestorage.com
    path: /releases
    channel: dev