{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] Guardar, [Ctrl+Shift+Enter] Guardar e Reenviar", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Enter] enviar, [<PERSON><PERSON>+Enter] que<PERSON> de linha, [Ctrl+Enter] enviar sem gerar", "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}": "{{count}} fi<PERSON><PERSON>(s) não suportado(s): {{files}}. Formatos suportados: {{formats}}", "{{count}} MCP servers imported": "{{count}} servidores MCP importados", "A provider with this ID already exists. Continuing will overwrite the existing configuration.": "Já existe um fornecedor com este ID. Ao continuar, a configuração existente será substituída.", "About": "Sobre", "About Chatbox": "Sobre o Chatbox", "about-introduction": "Um cliente de desktop AI simples e fácil de usar, que suporta os mais avançados modelos de linguagem global, tornando a tecnologia de inteligência artificial de ponta numa ferramenta de produtividade acessível.", "about-slogan": "Melhore a eficiência com IA, o melhor parceiro para trabalho e estudo", "Access to all future premium feature updates": "Acesso a todas as futuras atualizações de recursos premium", "Action": "Ação", "Activate License": "Ativar Licença", "Activating...": "Ativando...", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add at least one model to check connection": "Adicionar pelo menos um modelo para verificar ligação", "Add Custom Provider": "<PERSON><PERSON><PERSON><PERSON>", "Add Custom Server": "<PERSON><PERSON><PERSON><PERSON>", "Add File": "<PERSON><PERSON><PERSON><PERSON>", "Add MCP Server": "Adicionar MCP Server", "Add or Import": "<PERSON><PERSON><PERSON><PERSON> ou Importar", "Add provider": "<PERSON><PERSON><PERSON><PERSON>", "Add Server": "<PERSON><PERSON><PERSON><PERSON>", "Add your first MCP server": "Adicione o seu primeiro servidor MCP", "advanced": "<PERSON>van<PERSON><PERSON>", "Advanced": "Avançado", "Advanced Mode": "<PERSON><PERSON>", "AI Model Provider": "Fornecedor do Modelo AI", "ai provider no implemented paint tips": "O fornecedor de AI atual ({{aiProvider}}) não suporta a funcionalidade de desenho, atualmente apenas Chatbox AI, OpenAI e Azure OpenAI suportam esta funcionalidade, se necessário, por favor <0>abra as configurações para alterar</0> o fornecedor de AI", "AI Settings": "Definições de AI", "AIHubMix integration in Chatbox offers 10% discount": "A integração do AIHubMix no Chatbox oferece 10% de desconto", "All data is stored locally, ensuring privacy and rapid access": "Todos os dados são armazenados localmente, garantindo privacidade e acesso rápido", "All major AI models in one subscription": "Todos os principais modelos de IA em uma subscrição", "All threads": "Todos os Tópicos", "already existed": "j<PERSON> <PERSON>e", "An easy-to-use AI client app": "Uma aplicação cliente de AI fácil de usar", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Ocorreu um erro ao processar a sua solicitação. Por favor, tente novamente mais tarde. Se este erro continuar, por favor envie um e-<NAME_EMAIL> para obter suporte.", "An error occurred while sending the message.": "Ocorreu um erro ao enviar a mensagem.", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "Uma implementação de servidor MCP que fornece uma ferramenta para resolução de problemas dinâmica e reflexiva através de um processo de pensamento estruturado.", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Ocorreu um erro desconhecido. Por favor, tente novamente mais tarde. Se este erro continuar, por favor envie um e-<NAME_EMAIL> para obter suporte.", "any number key": "qualquer tecla numérica", "api error tips": "Ocorreu um erro de {{aiProvider}}, geralmente causado por configurações erradas ou problemas de conta. Verifique as definições de AI e a situação da conta, ou <0>clique aqui para ver a documentação de perguntas frequentes</0>.", "api host": "Domínio API", "API Host": "Hospedagem da API", "api key": "Chave API", "API Key": "Chave API", "API KEY & License": "Chave API & Licença", "API key invalid!": "Chave API inválida!", "API Key is required to check connection": "A Chave de API é necessária para verificar a ligação", "API Mode": "Modo API", "api path": "caminho da API", "API Path": "<PERSON><PERSON><PERSON> da <PERSON>", "Are you sure you want to delete the knowledge base": "Tem a certeza que pretende eliminar a base de conhecimento", "Are you sure you want to delete this server?": "Tem a certeza que quer apagar este servidor?", "Arguments": "Argumentos", "assistant": "<PERSON><PERSON><PERSON>", "Attach Image": "Anexar Imagem", "Attach Link": "Anexar Link", "Auther Message": "“No início, só queria desenvolver uma ferramenta conveniente para uso próprio, não esperava que tantas pessoas gostassem dela! Se estiver disposto a apoiar o meu trabalho de desenvolvimento, considere fazer uma doação, muito obrigado.”", "Auto": "Automático", "Auto (Use Chat Model)": "Automático (Usar Modelo de Chat)", "Auto (Use Chatbox AI)": "Automático (Usar Chatbox AI)", "Auto (Use Last Used)": "Automático (Usar Último Usado)", "Auto-collapse code blocks": "Auto-esconder blocos de código", "Auto-Generate Chat Titles": "Auto-G<PERSON><PERSON> t<PERSON>", "Auto-preview artifacts": "Pré-visualização automática de artefatos", "Automatic updates": "Atualizações automáticas", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "Renderizar automaticamente artefatos gerados (por exemplo, HTML com CSS, JS, Tailwind)", "Azure API Key": "Chave da <PERSON> Azure", "Azure API Version": "Versão da API Azure", "Azure Dall-E Deployment Name": "Nome de Implantação do Azure Dall-E", "Azure Deployment Name": "Nome de Implementação Azure", "Azure Endpoint": "Ponto final Azure", "Back to Previous": "Voltar ao Anterior", "Beta updates": "Atualizações beta", "Browsing and retrieving information from the internet.": "Navegação Web, procurando e obtendo informações da internet.", "Builtin MCP Servers": "Servidores MCP Integrados", "Can be activated on up to 5 devices": "Pode ser ativado em até 5 dispositivos", "cancel": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "cannot be empty": "não pode estar vazio", "Capabilities": "Capacidades", "Changelog": "Registro de alterações", "characters": "Caracteres", "chat": "Conversa", "Chat": "Conversa", "Chat History": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Settings": "Definições de Conversa", "Chatbox AI Advanced Model Quota": "Quota do modelo avançado Chatbox AI", "Chatbox AI Cloud": "Chatbox AI Nuvem", "Chatbox AI Image Quota": "Quota de Imagens do Chatbox AI", "Chatbox AI License": "Licença Chatbox AI", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI oferece uma solução de IA fácil de usar para ajudá-lo a aumentar a produtividade", "Chatbox AI provides all the essential model support required for knowledge base processing": "A Chatbox AI fornece todo o suporte essencial de modelos necessário para o processamento de bases de conhecimento", "Chatbox AI Quota": "Chatbox AI Quota", "Chatbox AI Standard Model Quota": "Quota do modelo padrão Chatbox AI", "Chatbox Featured": "Destaque do Chatbox", "Chatbox OCRs images with this model and sends the text to models without image support.": "O Chatbox faz OCR de imagens com este modelo e envia o texto para modelos sem suporte a imagens.", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "O Chatbox respeita a sua privacidade e só carrega dados anônimos de erros e eventos quando necessário. <PERSON><PERSON> mudar as suas preferências a qualquer momento nas configurações.", "Chatbox will automatically use this model to construct search term.": "O Chatbox usará automaticamente este modelo para construir termos de pesquisa.", "Chatbox will automatically use this model to rename threads.": "O Chatbox usará automaticamente este modelo para renomear tópicos.", "Chatbox will use this model as the default for new chats.": "O Chatbox usará este modelo como padrão para novos chats.", "ChatGLM-6B URL Helper": "Suporte ao modelo de código aberto <1>ChatGLM-6B</1> através da <0>API</0>", "ChatGLM-6B Warnning for Chatbox-Web": "Parece que está a usar a versão web do Chatbox, que pode ter problemas de CORS com o ChatGLM-6B. Recomenda-se o download do cliente Chatbox para evitar problemas potenciais.", "Check": "Verificar", "Check Update": "Verificar atualizações", "Child-inappropriate content": "Conte<PERSON>do inapropriado para crianças", "Choose a file": "Escolher um ficheiro", "Choose a knowledge base": "Escolher uma base de conhecimento", "Chunk": "Fragmento", "chunks": "Blocos", "clean": "Limpar", "clean it up": "Limpar", "Clear All Messages": "<PERSON><PERSON> as <PERSON><PERSON><PERSON>", "Clear Conversation List": "Limpar Lista de Conversas", "Click here to set up": "Clique aqui para configurar", "Click to view license details and quota usage": "Clique para ver os detalhes da licença e uso da quota", "close": "<PERSON><PERSON><PERSON>", "Code Search": "Pesquisa de Código", "Collapse": "Colapsar", "Coming soon": "Em breve", "Command": "Comand<PERSON>", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Configuration Parsed Successfully": "Configuração Analisada Com Sucesso", "Configure MCP server manually": "Configurar servidor MCP manualmente", "Confirm": "Confirmar", "Confirm deletion?": "Confirmar exclusão?", "Confirm to delete this custom provider?": "Confirmar a eliminação deste fornecedor personalizado?", "Confirm?": "Confirmar?", "Connection failed!": "Conexão falhou!", "Connection successful!": "Conexão bem-sucedida!", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "A conexão com {{aiProvider}} falhou. Isso geralmente ocorre devido a configurações incorretas ou problemas de conta {{aiProvider}}. Por favor, <buttonOpenSettings>verifique suas configurações</buttonOpenSettings> e verifique o status de sua conta {{aiProvider}}, ou compre uma <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> para desbloquear todos os modelos avançados instantaneamente sem nenhuma configuração.", "Continue this thread": "Continuar este tópico", "Continue this Thread": "Continuar este Tópico", "Conversation Settings": "Definições de Conversa", "copied to clipboard": "Copiado para a área de transferência", "Copilot Avatar URL": "URL do Avatar do Copiloto", "Copilot Name": "Nome do Copiloto", "Copilot Prompt": "Prompt de Copiloto", "Copilot Prompt Demo": "Você é um tradutor, seu trabalho é traduzir do chinês para o inglês", "copy": "Copiar", "Copy reasoning content": "<PERSON><PERSON><PERSON> con<PERSON><PERSON><PERSON> rac<PERSON>", "Create": "<PERSON><PERSON><PERSON>", "Create a New Conversation": "Criar uma Nova Conversa", "Create a New Image-Creator Conversation": "Criar uma Nova Conversa de Criação de Imagens", "Create File": "<PERSON><PERSON><PERSON>", "Create First Knowledge Base": "Criar Primeira Base de Conhecimento", "Create Knowledge Base": "Criar Base de Conhecimento", "Create New Copilot": "Criar Novo Copiloto", "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.": "Crie a sua primeira base de conhecimento para começar a adicionar documentos e melhorar as suas conversas de IA com informação contextual.", "creative": "Criativo", "Current conversation configured with specific model settings": "Conversa atual configurada com definições específicas do modelo", "Current model {{modelName}} does not support image input, using OCR to process images": "O modelo atual {{modelName}} não suporta entrada de imagem, a usar OCR para processar imagens", "Current thread": "Tópico Atual", "Custom": "Personalizado", "Custom MCP Servers": "Servidores MCP Personalizados", "Custom Model": "<PERSON><PERSON>iza<PERSON>", "Custom Model Name": "Nome do Modelo Personalizado", "Customize settings for the current conversation": "Personalizar configurações para a conversa atual", "Dark Mode": "<PERSON><PERSON>", "Data Backup": "<PERSON><PERSON>", "Data Backup and Restore": "Backup e Restauração de Dados", "Data Restore": "Restauração de Dados", "Deactivate": "Desativar", "Deeply thought": "Profundamente pensado", "Default Assistant Avatar": "Avatar Padrão do Assistente", "Default Chat Model": "<PERSON><PERSON> de Chat <PERSON>", "Default Models": "<PERSON><PERSON>", "Default Prompt for New Conversation": "Prompt padrão para nova conversa", "Default Settings for New Conversation": "Definições Predefinidas para Nova Conversa", "Default Thread Naming Model": "Modelo Padrão de Nomeação de Tópicos", "delete": "Eliminar", "Delete": "Eliminar", "delete confirmation": "Esta ação irá eliminar permanentemente o conteúdo de {{sessionName}}. Tem a certeza de que deseja continuar?", "Delete Current Session": "Eliminar a sessão atual", "Delete File": "Eliminar <PERSON>", "Delete Knowledge Base": "Eliminar Base de Conhecimento", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "Publicar conteúdo HTML para EdgeOne Pages e obter um URL público acessível.", "Description": "Descrição", "Details": "<PERSON><PERSON><PERSON>", "Disabled": "Desabilitado", "display": "<PERSON><PERSON><PERSON>", "Display": "<PERSON><PERSON><PERSON>", "Display Settings": "Definições de Exibição", "Documents": "Documentos", "Donate": "<PERSON><PERSON>", "Done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Drag and drop files here, or click to browse": "Arraste e largue ficheiros aqui, ou clique para procurar", "Drop files here": "<PERSON><PERSON><PERSON> os ficheiros para aqui", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "Devido às limitações de processamento local, <Link>Serviço Chatbox AI</Link> é recomendado para melhorar as capacidades de processamento de documentos e obter melhores resultados.", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "Devido às limitações de processamento local, <Link>Serviço Chatbox AI</Link> é recomendado para melhorar as capacidades de análise de páginas web, especialmente para páginas web dinâmicas.", "E-mail": "E-mail", "e.g., Model Name, Current Date": "por exemplo, Nome do Modelo, Data Atual", "Easy Access": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "Edit": "<PERSON><PERSON>", "Edit Avatars": "<PERSON><PERSON>", "Edit default assistant avatar": "Editar avatar do assistente padrão", "Edit File": "<PERSON><PERSON>", "Edit Knowledge Base": "Editar Base de Conhecimento", "Edit MCP Server": "<PERSON><PERSON>", "Edit Model": "<PERSON><PERSON>", "Edit Thread Name": "Editar Nome do Tópico", "Edit user avatar": "Editar avatar do utilizador", "Embedding": "Incorporação", "Embedding Model": "Modelo de Embedding", "Enable optional anonymous reporting of crash and event data": "Ativar o relatório anônimo opcional de dados de falhas e eventos", "Enable Thinking": "Ativar Pen<PERSON>nto", "Enabled": "<PERSON><PERSON>do", "Ending with / ignores v1, ending with # forces use of input address": "Terminar com / ignora v1, terminar com # força o uso do endereço de entrada", "Enjoying Chatbox?": "Gostando do Chatbox?", "Enter": "Tecla Enter", "Environment Variables": "Variáveis de Ambiente", "Error Reporting": "Relatório de Erros", "expand": "Expandir", "Expand": "Expandir", "Expansion Pack Quota": "Quota do Pacote de Expansão", "Explore (community)": "Explorar (comunidade)", "Explore (official)": "Explorar (oficial)", "export": "Exportar", "Export Chat": "Exportar Chat", "Export Selected Data": "Exportar Dados Selecionados", "Exporting...": "A exportar...", "extension": "Extensões", "Failed": "Fal<PERSON>", "Failed to activate license, please check your license key and network connection": "Falha ao ativar a licença, por favor verifique sua chave de licença e conexão de rede", "Failed to create knowledge base, Error: {{error}}": "Falha ao criar base de conhecimento, Erro: {{error}}", "Failed to export file: {{error}}": "Falha ao exportar ficheiro: {{error}}", "Failed to fetch Chatbox AI models config, Error: {{error}}": "Falha ao obter a configuração dos modelos de IA do Chatbox, Erro: {{error}}", "Failed to fetch file chunks, Error: {{error}}": "<PERSON>alha ao obter blocos de ficheiro, Erro: {{error}}", "Failed to fetch files, Error: {{error}}": "<PERSON><PERSON>ha ao obter fi<PERSON><PERSON>, Erro: {{error}}", "Failed to fetch knowledge base list, Error: {{error}}": "Falha ao obter a lista de bases de conhecimento, Erro: {{error}}", "Failed to fetch models": "Falha ao buscar modelos", "Failed to import provider": "Falha ao importar provedor", "Failed to load Chatbox AI models configuration": "Falha ao carregar a configuração dos modelos de AI do Chatbox", "Failed to open file dialog: {{error}}": "Falha ao abrir a caixa de diálogo de ficheiros: {{error}}", "Failed to read from clipboard": "Falha ao ler da área de transferência", "Failed to save file: {{error}}": "<PERSON>alha ao guardar ficheiro: {{error}}", "Failed to update knowledge base, Error: {{error}}": "Falha ao atualizar base de conhecimento, Erro: {{error}}", "Failed to upload {{filename}}: {{error}}": "Falha ao carregar {{filename}}: {{error}}", "FAQs": "FAQs", "Favorite": "<PERSON><PERSON><PERSON><PERSON>", "Feedback": "<PERSON><PERSON><PERSON>", "Fetch": "Buscar", "File Chunks": "Fragmentos de Ficheiro", "File Chunks Preview": "Pré-visualização de Fragmentos de Ficheiro", "File saved to {{uri}}": "<PERSON><PERSON><PERSON> guardado em {{uri}}", "File Search": "Pesquisa de Ficheiros", "File Size": "Tamanho do Ficheiro", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "Tipo de arquivo não suportado. Os tipos suportados incluem txt, md, html, doc, docx, pdf, excel, pptx, csv e todos os arquivos baseados em texto, incluindo arquivos de código.", "Focus on the Input Box": "Focar na Caixa de Entrada", "Focus on the Input Box and Enter Web Browsing Mode": "Focar no campo de entrada e entrar no modo de navegação web", "Follow me on Twitter(X)": "Siga-me no Twitter(X)", "Follow System": "<PERSON><PERSON><PERSON>", "Font Size": "<PERSON><PERSON><PERSON>", "font size changed, effective after next launch": "<PERSON><PERSON><PERSON> da fonte alterado, efetivo após o próximo lançamento", "Format": "Formato", "Full-text search of chat history (coming soon)": "Pesquisa de texto completo do histórico de conversas (em breve)", "Function": "Função", "General Settings": "Definições Gerais", "Generate More Images Below": "<PERSON><PERSON><PERSON> A<PERSON>ix<PERSON>", "Get API Key": "Obter Chave API", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "Obtenha uma melhor conectividade e estabilidade com a aplicação Chatbox para desktop. <a>Baixe agora</a>.", "Get Files Meta": "Obter Metadados de Ficheiros", "Get License": "Obter Licença", "get more": "Obter mais", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "<PERSON><PERSON><PERSON><PERSON> no<PERSON> ou ofensivo", "Hassle-free setup": "Configuração sem complicações", "Hate speech or harassment": "Discurso de ódio ou assédio", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "Aqui você pode adicionar e gerenciar vários fornecedores de modelos personalizados. Contanto que a API do fornecedor seja compatível com o modo de API selecionado, você pode conectá-lo e usá-lo sem problemas no Chatbox.", "Hide": "Ocultar", "High": "Alto", "Home Page": "Página Inicial", "Homepage": "Página inicial", "Hotkeys": "Atalhos de teclado", "How to use?": "<PERSON> usar?", "ID": "ID", "Ideal for both work and educational scenarios": "Ideal para cenários de trabalho e educação", "Ideal for work and study": "Ideal para trabalho e estudo", "Image Creator": "<PERSON><PERSON><PERSON>", "Image Creator Intro": "Olá! Eu sou o Criador de Imagens Chatbox, a 'máquina impiedosa' de fazer imagens. Posso criar belas imagens com base na tua descrição, desde paisagens encantadoras até personagens vívidos, ícones de apps ou conceitos abstratos... (๑•́ ₃ •̀๑) Eh... sou um pouco introvertido, por isso **por favor, diz-me diretamente o que desejas visualizar**, e eu vou concentrar todos os meus pixels para realizar a tua imaginação. Agora, deixa a tua imaginação voar!", "Image Style": "<PERSON><PERSON><PERSON>", "Import and Restore": "Importar e Restaurar", "Import Error": "Erro de Importação", "Import failed, unsupported data format": "Falha na importação, formato de dados não suportado", "Import from clipboard": "Importar da área de transferência", "Import from JSON in clipboard": "Importar de JSON na área de transferência", "Import MCP servers from JSON in your clipboard": "Importar servidores MCP de JSON na sua área de transferência", "Importing...": "A importar...", "Improve Network Compatibility": "Melhorar a compatibilidade com a rede", "Inject default metadata": "Injetar metadados padrão", "Insert a New Line into the Input Box": "Inserir uma Nova Linha na Caixa de Entrada", "Instruction (System Prompt)": "Instrução (Prompt do Sistema)", "Invalid deep link config format": "Formato de configuração de Deep Link inválido", "Invalid provider configuration format": "Formato de configuração do provedor inválido", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "Parâmetros de pedido inválidos detectados. Por favor, tente novamente mais tarde. Falhas persistentes podem indicar uma versão de software desatualizada. Considere atualizar para aceder às últimas melhorias de desempenho e funcionalidades.", "It only takes a few seconds and helps a lot.": "<PERSON><PERSON> leva apenas alguns segundos e ajuda muito.", "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest": "<PERSON><PERSON> apenas as <0>{{N}}</0> conversas mais recentes na lista e eliminar permanentemente as restantes", "Key Combination": "Combinação de Teclas", "Keyboard Shortcuts": "Atalhos de Teclado", "Knowledge Base": "Base de Conhecimento", "Knowledge Base Debug": "Depuração da Base de Conhecimento", "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.": "A funcionalidade da Base de Conhecimento não está disponível no Windows ARM64 devido a problemas de compatibilidade de bibliotecas. Esta funcionalidade é suportada no Windows x64, macOS e Linux.", "Language": "Língua", "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.": "Ficheiro grande detetado. Os blocos serão carregados em lotes de {{count}} para otimizar o desempenho.", "Last Session": "Última Sessão", "LaTeX Rendering (Requires Markdown)": "Renderização LaTeX (Requer Markdown)", "Launch at system startup": "Iniciar automaticamente no arranque do sistema", "License Activated": "Licença Ativada", "License expired, please check your license key": "Licença expirada, por favor verifique a sua chave de licença", "License Expiry": "Expiração da Licença", "License not found, please check your license key": "Licença não encontrada, por favor verifique a sua chave de licença", "License Plan Overview": "Visão Geral do Plano de Licença", "lifetime license": "licença vitalícia", "Light Mode": "<PERSON><PERSON>", "List Files": "<PERSON><PERSON>", "Load More Chunks": "<PERSON><PERSON><PERSON>", "Loading chunks...": "A carregar blocos...", "Loading files...": "A carregar ficheiros...", "Loading more chunks...": "A carregar mais blocos...", "Loading webpage...": "Carregando página web...", "Local (stdio)": "Local (stdio)", "Local Mode": "Modo Local", "Low": "Baixo", "Make sure you have the following command installed:": "Certifique-se de que tem o seguinte comando instalado:", "Manage License and Devices": "Gerir Licença e Dispositivos", "Manually": "Manualmente", "Markdown Rendering": "Renderização Markdown", "Max Message Count in Context": "Contagem Máxima de Mensagens no Contexto", "Max Output Tokens": "Máximo de tokens de saída", "max tokens in context": "Máximo de tokens no contexto", "max tokens to generate": "Máximo de tokens para gerar", "Maybe Later": "<PERSON><PERSON><PERSON> mais tarde", "MCP server added": "Servidor MCP adicionado", "MCP server for accessing arXiv papers": "Servidor MCP para aceder a artigos arXiv", "MCP Settings": "Definições do MCP", "Medium": "Médio", "Mermaid Diagrams & Charts Rendering": "Renderização de Diagramas e Gráficos Mermaid", "meticulous": "Meticuloso", "MIME Type": "Tipo MIME", "Misleading information": "Informação enganosa", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "Dispositivos móveis temporariamente não suportam a análise local deste tipo de ficheiro. Por favor, utilize ficheiros de texto (txt, markdown, etc.) ou utilize <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> para análise de documentos baseada na nuvem.", "model": "<PERSON><PERSON>", "Model": "<PERSON><PERSON>", "Model ID": "ID do Modelo", "Model Provider": "Fornecedor do Modelo", "Model Type": "Tipo de Modelo", "Models": "Modelos", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "Modifique a criatividade das respostas da IA; quanto maior o valor, mais aleatórias e intrigantes as respostas se tornam, enquanto um valor mais baixo garante maior estabilidade e fiabilidade.", "More Images": "<PERSON><PERSON>", "Move to Conversations": "Mover para Conversas", "My Assistant": "<PERSON><PERSON>", "My Copilots": "<PERSON><PERSON>", "name": "Nome", "Name": "Nome", "Name is required": "O nome é obrigatório", "Natural": "Natural", "Navigate to the Next Conversation": "Navegar para a Próxima Conversa", "Navigate to the Next Option (in search dialog)": "Navegar para a Próxima Opção (no diálogo de pesquisa)", "Navigate to the Previous Conversation": "Navegar para a Conversa Anterior", "Navigate to the Previous Option (in search dialog)": "Navegar para a Opção Anterior (no diálogo de pesquisa)", "Navigate to the Specific Conversation": "Navegar para a Conversa Específica", "network error tips": "Erro de rede. Verifique o estado atual da sua rede e a conexão com {{host}}.", "Network Proxy": "Proxy de Rede", "network proxy error tips": "Devido ao endereço de proxy {{proxy}} que configurou, verifique se o servidor proxy está a funcionar corretamente ou considere eliminar o endereço de proxy nas definições.", "New": "Novo", "New Chat": "Nova conversa", "New Images": "Novas Imagens", "New knowledge base name": "Nome da nova base de conhecimento", "New Thread": "Novo Tópico", "Nickname": "Apelido", "No": "Não", "No chunks available. Try converting the file to a text format before adding it to the knowledge base.": "Nenhum fragmento disponível. Tente converter o ficheiro para um formato de texto antes de o adicionar à base de conhecimento.", "No documents yet": "Nenhum documento ainda", "No eligible models available": "Nenhum modelo elegível disponível", "No files were dropped": "<PERSON>en<PERSON> ficheiro foi largado", "No Knowledge Base Yet": "Ainda sem Base de Conhecimento", "No Limit": "Se<PERSON>", "No MCP servers parsed from clipboard": "Nenhum servidor MCP analisado da área de transferência", "No permission to write file": "Sem permissão para escrever ficheiro", "No results found": "Nenhum resultado encontrado", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "Nenhum resultado de pesquisa encontrado. Por favor, use outro <OpenExtensionSettingButton>provedor de pesquisa</OpenExtensionSettingButton> ou tente novamente mais tarde.", "None": "<PERSON><PERSON><PERSON>", "not available in browser": "não disponível no navegador, por favor descarregue a aplicação de desktop", "Not set": "<PERSON><PERSON> definido", "Nothing found...": "Nada encontrado...", "Number of Images per Reply": "Número de Imagens por Resposta", "OCR Model": "Modelo OCR", "One-click MCP servers for Chatbox AI subscribers": "Servidores MCP de um clique para subscritores do Chatbox AI", "OpenAI API Compatible": "Compatível com API OpenAI", "Operations": "Operações", "optional": "Opcional", "or": "ou", "Or become a sponsor": "Ou torne-se um patrocinador", "Other concerns": "Outros problemas", "Paste long text as a file": "Colar texto longo como um ficheiro", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "Colar texto longo como um ficheiro para manter as conversas limpas e reduzir o uso de tokens com cache de prompt.", "Pause": "Pausa", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, Código...", "Pending": "Pendente", "Platform Not Supported": "Plataforma Não Suportada", "Please describe the content you want to report (Optional)": "Por favor, descreva o conteúdo que deseja reportar (opcional)", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Por favor, certifique-se de que o Serviço Remoto LM Studio pode se conectar remotamente. Para mais de<PERSON>hes, consulte <a>este tutorial</a>.", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Por favor, certifique-se de que o Serviço Remoto Ollama consegue conectar-se remotamente. Para mais de<PERSON>hes, consulte <a>este tutorial</a>.", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "Por favor, note que, como uma ferramenta cliente, o Chatbox não pode garantir a qualidade do serviço e a privacidade dos dados dos fornecedores de modelos. Se você está procurando um serviço de modelo estável, confiável e que proteja a privacidade, considere <a>Chatbox AI</a>.", "Please select a model": "Por favor, selecione um modelo", "Please test before saving": "Por favor, teste antes de guardar", "Please wait about 20 seconds": "Por favor, aguarde cerca de 20 segundos", "pre-sale discount": "desconto pré-venda", "premium": "Edição Premium", "Premium Activation": "Ativação Premium", "Premium License Activated": "Licença Premium Ativada", "Premium License Key": "Chave de Licença Premium", "Press hotkey": "Pressionar atalho", "Preview": "Pré-visualizar", "Privacy Policy": "Política de Privacidade", "Processing failed": "Processamento falhou", "Processing...": "A processar...", "Prompt": "Prompt", "Provider Already Exists": "<PERSON><PERSON><PERSON>", "Provider configuration is valid and ready to import": "A configuração do provedor é válida e pronta para importar", "Provider Details": "Detalhes do Provedor", "Provider not found": "Fornecedor não encontrado", "Provider unavailable": "Fornecedor indisponível", "proxy": "Proxy", "Proxy Address": "Endereço do Proxy", "Purchase": "<PERSON><PERSON><PERSON>", "QR Code": "Código QR", "Query Knowledge Base": "Consultar Base de Conhecimento", "Quota Reset": "<PERSON><PERSON> da <PERSON>", "quote": "Citar", "Rate Now": "Avaliar agora", "Read File Chunks": "Ler Fragmentos de Ficheiro", "Reading file...": "Lendo arquivo...", "Reasoning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RedNote": "<PERSON><PERSON>", "Refresh": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON>", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "Regule o volume das mensagens históricas enviadas à IA, encontrando um equilíbrio harmonioso entre a profundidade de compreensão e a eficiência das respostas.", "Remote (http/sse)": "<PERSON><PERSON> (http/sse)", "rename": "Renomear", "Reply Again": "Responder Novamente", "Reply Again Below": "Responder Novamente Abaixo", "report": "Reportar", "Report Content": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>ar", "Report Content ID": "ID do Conteúdo a Reportar", "Report Type": "T<PERSON>o de Reporte", "Rerank": "Reordenar", "Rerank Model": "Modelo de Reordenação", "Rerank Model (optional)": "Modelo de Reclassificação (opcional)", "reset": "Repor", "Reset": "Repor", "Reset All Hotkeys": "Redefinir todos os atalhos de teclado", "Reset to Default": "Repor para o Padrão", "Reset to Global Settings": "Redefinir para as Configurações Globais", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Resume": "<PERSON><PERSON><PERSON>", "Retrieve License": "Recuperar Licença", "Retrieves up-to-date documentation and code examples for any library.": "Recupera documentação atualizada e exemplos de código para qualquer biblioteca.", "Retry": "Tentar Novamente", "Roadmap": "Roteiro", "save": "Guardar", "Save": "<PERSON><PERSON>", "Save & Resend": "Guardar e Reenviar", "Scope": "Âmbito", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search All Conversations": "<PERSON><PERSON><PERSON><PERSON> <PERSON> as <PERSON><PERSON><PERSON>", "Search in Current Conversation": "Pesquisar na Conversa Atual", "Search models": "Pesquisar modelos", "Search Provider": "<PERSON><PERSON><PERSON>es<PERSON>", "Search query": "Consulta de pesquisa", "Search Term Construction Model": "Modelo de Construção de Termos de Pesquisa", "Search...": "Pesquisar...", "Select and configure an AI model provider": "Selecionar e configurar um fornecedor de modelo de IA", "Select File": "Selecionar Arquivo", "Select Knowledge Base": "Selecionar Base de Conhecimento", "Select Model": "Selecionar Modelo", "Select the Current Option (in search dialog)": "Selecionar a Opção Atual (no diálogo de pesquisa)", "send": "Enviar", "Send": "Enviar", "Send Without Generating Response": "Envia<PERSON> Se<PERSON> Gera<PERSON>", "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.": "Definir o número máximo de tokens para a saída do modelo. Por favor, defina-o dentro do intervalo aceitável do modelo, caso contr<PERSON>, poder<PERSON> ocorrer erros.", "Setting the avatar for Copilot": "Configurar o avatar para Copiloto", "settings": "definições", "Settings": "Definições", "Sexual content": "<PERSON><PERSON><PERSON><PERSON>", "Share File": "<PERSON><PERSON><PERSON>", "Share with Chatbox": "Partilhar com Chatbox", "Show": "Mostrar", "Show all ({{x}})": "Mostrar tudo ({{x}})", "show first token latency": "Mostrar latência do primeiro token", "Show in Thread List": "Mostrar na lista de tópicos", "show message timestamp": "Mostrar carimbo de data/hora da mensagem", "show message token count": "Mostrar contagem de tokens da mensagem", "show message token usage": "Mostrar uso de tokens da mensagem", "show message word count": "Mostrar contagem de palavras da mensagem", "show model name": "Mostrar nome do modelo", "Show/Hide the Application Window": "Mostrar/Ocultar a Janela da Aplicação", "Show/Hide the Search Dialog": "Mostrar/Ocultar o Diálogo de Pesquisa", "Showing {{loaded}} of {{total}} chunks": "A mostrar {{loaded}} de {{total}} blocos", "Showing first {{count}} chunks": "A mostrar os primeiros {{count}} pedaços", "Smartest AI-Powered Services for Rapid Access": "Serviços AI mais inteligentes para acesso rápido", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "<PERSON><PERSON><PERSON><PERSON>, o modelo atual {{model}} API em si não suporta a compreensão de imagens. Se você precisar enviar imagens, por favor, mude para outro modelo ou use os modelos recomendados <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "<PERSON><PERSON><PERSON><PERSON>, o modelo atual {{model}} API em si não suporta a compreensão de imagens. Se você precisar enviar imagens, por favor, mude para outro modelo.", "Spam or advertising": "Spam ou propaganda", "Special thanks to the following sponsors:": "Um agradecimento especial aos seguintes patrocinadores:", "Specific model settings": "Definições específicas do modelo", "Spell Check": "Verificação Ortográfica", "star": "Favoritar", "Start a New Thread": "Começar um Novo Tópico", "Start Setup": "Iniciar <PERSON>", "Startup Page": "Página de Início", "Status": "Estado", "stop generating": "<PERSON><PERSON> <PERSON>", "Stream output": "Saída em fluxo", "submit": "Enviar", "Successfully uploaded {{count}} file(s)": "{{count}} fi<PERSON><PERSON>s carregados com sucesso", "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.": "{{success}} de {{total}} ficheiros carregados com sucesso. {{failed}} ficheiros falharam.", "Support for ChatBox development": "Suporte para o desenvolvimento do ChatBox", "Support jpg or png file smaller than 5MB": "Suporte para ficheiros jpg ou png menores que 5MB", "Supported formats": "Formatos suportados", "Supports a variety of advanced AI models": "Suporta uma variedade de modelos de IA avançados", "Survey": "Pesquisa", "Switch": "<PERSON><PERSON>", "system": "Sistema", "Tavily API Key": "Chave <PERSON> Tavily", "temperature": "Temperatura (Rigor e Imaginação)", "Temperature": "Temperatura", "Terminal": "Terminal", "Test": "<PERSON>e", "Thank you for your report": "<PERSON><PERSON><PERSON> pelo seu relatório", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "A API {{model}} não suporta ficheiros. Por favor, baixe <LinkToHomePage>a aplicação de desktop</LinkToHomePage> para processamento local.", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "A API {{model}} não suporta ficheiros. Por favor, use <LinkToAdvancedFileProcessing>modelos Chatbox AI</LinkToAdvancedFileProcessing> em vez disso, ou baixe <LinkToHomePage>a aplicação de desktop</LinkToHomePage> para processamento local.", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "A API {{model}} não suporta links. Por favor, baixe <LinkToHomePage>a aplicação de desktop</LinkToHomePage> para processamento local.", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "A API {{model}} não suporta links. Por favor, use <LinkToAdvancedUrlProcessing>modelos Chatbox AI</LinkToAdvancedUrlProcessing> em vez disso, ou baixe <LinkToHomePage>a aplicação de desktop</LinkToHomePage> para processamento local.", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "A API {{model}} não suporta a compreensão de documentos. Você pode baixar <LinkToHomePage>a aplicação de desktop Chatbox</LinkToHomePage> para análise de documentos local.", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "A API {{model}} não suporta a compreensão de documentos. Você pode usar <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> para análise de documentos em nuvem ou <LinkToHomePage>a aplicação de desktop Chatbox</LinkToHomePage> para análise de documentos local.", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "A API {{model}} em si não suporta o envio de ficheiros. Devido à complexidade do processamento de ficheiros localmente, o Chatbox só processa ficheiros baseados em texto (incluindo código).", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "A API {{model}} em si não suporta o envio de ficheiros. Devido à complexidade do processamento de ficheiros localmente, o Chatbox só processa ficheiros baseados em texto (incluindo código). Para suporte a formatos de ficheiro adicionais e capacidades de compreensão de documentos aprimoradas, <LinkToAdvancedFileProcessing>Serviço Chatbox AI</LinkToAdvancedFileProcessing> é recomendado.", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "O modelo atual {{model}} API não suporta navegação web. Modelos suportados: {{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "O modelo atual {{model}} API não suporta navegação web. Modelos suportados: <OpenMorePlanButton>modelos Chatbox AI</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "Os dados de cache para o arquivo não foram encontrados. Por favor, crie uma nova conversa ou atualize o contexto, e então envie o arquivo novamente.", "The current model {{model}} does not support sending links.": "O modelo atual {{model}} não suporta o envio de links.", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "O modelo atual {{model}} não suporta o envio de links. Modelos atualmente suportados: Chatbox AI.", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "O tamanho do arquivo excede o limite de 50MB. Por favor, reduza o tamanho do arquivo e tente novamente.", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "O arquivo que enviou expirou. Para proteger a sua privacidade, todos os dados de cache relacionados ao arquivo foram limpos. Você precisa criar uma nova conversa ou atualizar o contexto, e então enviar o arquivo novamente.", "The Image Creator plugin has been activated for the current conversation": "O plugin <PERSON><PERSON>or de Imagens foi ativado para a conversa atual", "The license key you entered is invalid. Please check your license key and try again.": "A chave de licença que introduziu é inválida. Por favor verifique a sua chave de licença e tente novamente.", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "O parâmetro topP controla a diversidade das respostas da AI: valores mais baixos tornam o resultado mais focado e previsível, enquanto valores mais altos permitem respostas mais variadas e criativas.", "Theme": "<PERSON><PERSON>", "Thinking": "A pensar", "Thinking Budget": "A pensar Orçamento", "Thinking Budget only works for 2.0 or later models": "O Orçamento de Raciocínio só funciona para modelos 2.0 ou posteriores", "Thinking Budget only works for 3.7 or later models": "Orçamento de Pensamento só funciona para 3.7 ou modelos posteriores", "Thinking Effort": "Esforço de Pensamento", "Thinking Effort only works for OpenAI o-series models": "Esforço de Pensamento só funciona para os modelos OpenAI da série o.", "This action cannot be undone. All documents and their embeddings will be permanently deleted.": "Esta ação não pode ser desfeita. Todos os documentos e os seus embeddings serão permanentemente eliminados.", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "Esta chave de licença atingiu o limite de ativação, <a>clique aqui</a> para gerir a licença e os dispositivos para desativar dispositivos antigos.", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "Este servidor permite que LLMs recuperem e processem conteúdo de páginas web, convertendo HTML para markdown para um consumo mais fácil.", "Thread History": "Histórico de Tópicos", "To access locally deployed model services, please install the Chatbox desktop version": "Para aceder aos serviços de modelos implantados localmente, por favor instale a versão desktop do Chatbox", "Toggle": "Alternar", "token": "Token", "Tool use": "Uso de Ferramentas", "Tool Use": "Uso de Ferramentas", "Tools": "Ferramentas", "Top P": "Top P", "Total Chunks": "Total de Blocos", "Type": "Tipo", "Type a command or search": "Digite um comando ou pesquisa", "Type your question here...": "Digite a sua pergunta aqui...", "Unknown": "Desconhecido", "unknown error tips": "Erro desconhecido. Verifique as definições de AI e a situação da conta, ou <0>clique aqui para ver a documentação de perguntas frequentes</0>.", "Unlock Copilot Avatar by Upgrading to Premium Edition": "Desbloqueie o avatar do copiloto ao atualizar para a edição Premium", "Unsaved settings": "Definições não guardadas", "unstar": "Desfavoritar", "Untitled": "<PERSON><PERSON>", "Update Available": "Atualização disponível", "Upload failed: {{error}}": "Carregamento falhou: {{error}}", "Upload Image": "<PERSON><PERSON><PERSON>", "Upload your first document to get started": "Carregue o seu primeiro documento para começar", "Upon import, changes will take effect immediately and existing data will be overwritten": "Após a importação, as alterações terão efeito imediato e os dados existentes serão sobrescritos", "Use My Own API Key / Local Model": "Usar minha própria API Key / Modelo Local", "Use proxy to resolve CORS and other network issues": "Usar proxy para resolver problemas de CORS e outros problemas de rede", "Used to extract text feature vectors, add in Settings - Provider - Model List": "Usado para extrair vetores de características de texto, adicionar em Definições - Fornecedor - Lista de Modelos", "Used to get more accurate search results": "Usado para obter resultados de pesquisa mais precisos", "Used to preprocess image files, requires models with vision capabilities enabled": "Usado para pré-processar ficheiros de imagem, requer modelos com capacidades de visão ativadas", "user": "Utilizador", "User Avatar": "Avatar do Utilizador", "User Terms": "Termos de Utilizador", "version": "Vers<PERSON>", "View All Copilots": "Ver Todos os Copilotos", "View historical threads": "Ver tópicos históricos", "View More Plans": "<PERSON><PERSON> <PERSON>", "Violence or dangerous content": "Violência ou conteúdo perigoso", "Vision": "Visão", "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>": "A capacidade de visão não está ativada para o Modelo {{model}}. Por favor, ative-a ou defina um modelo OCR predefinido em <OpenSettingButton>Definições</OpenSettingButton>", "Vision Model": "<PERSON><PERSON>", "Vision Model (optional)": "<PERSON><PERSON> de Visão (opcional)", "Vision, Drawing, File Understanding and more": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Compreensão de Ficheiros e mais", "Vivid": "<PERSON>í<PERSON><PERSON>", "Web Browsing": "Navegação Web", "Web browsing (coming soon)": "Navegação na Web (em breve)", "Web Browsing...": "Navegação Web...", "Web Search": "Pesquisa na Internet", "WeChat": "WeChat", "What can I help you with today?": "Como posso ajudar hoje?", "Yes": "<PERSON>m", "You are already a Premium user": "Já é um utilizador Premium", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "Você excedeu o limite de taxa para o serviço Chatbox AI. Por favor, tente novamente mais tarde.", "You have no more Chatbox AI quota left this month.": "Já não tem mais cota de Chatbox AI este mês.", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "Você atingiu a sua cota mensal para o modelo {{model}}. Por favor, <OpenSettingButton>vá às Configurações</OpenSettingButton> para mudar para um modelo diferente, ver o uso da sua cota, ou atualizar o seu plano.", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "Você selecionou Chatbox AI como o fornecedor do modelo, mas ainda não foi introduzida uma chave de licença. Por favor <OpenSettingButton>clique aqui para abrir Configurações</OpenSettingButton> e introduza a sua chave de licença, ou escolha um fornecedor de modelo diferente.", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "Você selecionou Chatbox AI como o provedor de pesquisa, mas ainda não introduziu uma chave de licença. Por favor <OpenSettingButton>clique aqui para abrir Configurações</OpenSettingButton> e introduza a sua chave de licença, ou escolha um <OpenExtensionSettingButton>provedor de pesquisa</OpenExtensionSettingButton> diferente.", "You have selected Tavily as the search provider, but a API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "Você selecionou Tavily como o provedor de pesquisa, mas ainda não introduziu uma chave de API. Por favor <OpenExtensionSettingButton>clique aqui para abrir Configurações</OpenExtensionSettingButton> e introduza a sua chave de API, ou escolha um provedor de pesquisa diferente.", "You have unsaved settings. Are you sure you want to leave?": "Tem definições não guardadas. Tem a certeza que deseja sair?", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "Sua subscrição ChatboxAI já inclui acesso a modelos de vários fornecedores. Não é necessário mudar de fornecedor - você pode selecionar diferentes modelos diretamente no ChatboxAI. Mudar de ChatboxAI para outros fornecedores exigirá seus respectivos API keys. <button>Voltar para ChatboxAI</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "A sua Licença atual (Chatbox AI Lite) não suporta o modelo {{model}}. Para usar este modelo, por favor <OpenMorePlanButton>atualize</OpenMorePlanButton> para Chatbox AI Pro ou um pacote de nível superior. Alternativamente, pode mudar para um modelo diferente através <OpenSettingButton>do acesso às configurações</OpenSettingButton>.", "Your license has expired. Please check your subscription or purchase a new one.": "A sua licença expirou. Por favor, verifique a sua subscrição ou adquira uma nova.", "Your rating on the App Store would help make Chatbox even better!": "Seu rating no App Store ajudará a tornar o Chatbox ainda melhor!"}