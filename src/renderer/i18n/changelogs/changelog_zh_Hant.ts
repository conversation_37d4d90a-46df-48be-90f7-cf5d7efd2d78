const changelog = `
## v1.15.1 - 2025.07.22

1. 支援關閉流式輸出
2. 支援設定 max token 參數
3. 修復移動端無法訪問 ollama 的問題
4. 調整輸入框樣式
5. 支援通過 deep link 一鍵導入 MCP 和提供方配置
6. 修復新對話消息某些條件下沒有被發出的問題

### v1.15.0 - 2025.07.07

1. 本地知識庫支援
2. 調整思考和工具調用訊息樣式

### v1.14.4 - 2025.07.03

1. 修復對話列表中編輯功能會導致程式崩潰的問題

### v1.14.3 - 2025.06.28

1. 修復移動端導出資料會導致閃退的問題
2. 新增全域模型參數設定
3. 修復一些 markdown 和 Latex 顯示問題
4. 修復 OpenRouter 部分模型思考內容不展示的問題
5. 兼容 MCP 環境變量中包含=號字符的問題

本次更新感謝 @jakub-nezasa 的貢獻

### v1.14.2 - 2025.06.19

1. 修復移動端換行會直接發送訊息的問題
2. 修復部分裝置上發送按鈕失效的問題
3. 調整新話題按鈕大小

### v1.14.1 - 2025.06.16

1. 修復重啟後模型提供方設定遺失的問題

### v1.14.0 - 2025.06.16

1. 桌面端支援 MCP 
2. 全新首頁設計
3. 新增火山引擎模型提供方
4. 修復 Azure 下自定義溫度無效的問題，現在可以將 o 系列模型的溫度設定為 1
5. 修復非 QWERT 鍵盤下快捷鍵錯誤

本次更新感謝 @Fr0benius 的貢獻

### v1.13.4 - 2025.06.09

1. 修復儲存性能問題
2. 修復英文語言下清理對話列表時，無法填寫保留數量的問題

### v1.13.3 - 2025.06.08

1. 修復自定義提供方無法設定 API Path 的問題
2. OpenAI、Claude、Gemini 部分模型支援設定思考程度參數

### v1.13.2 - 2025.05.30

1. 修復窗口對話標題列無法拖動的問題

### v1.13.1 - 2025.05.28

1. 大幅重構設定 UI
2. 在聊天會話中快速切換不同的模型提供方
3. 修復將話題移動到會話時，會複製原會話下所有話題的問題
4. 修復部分情況對話搜索無結果的問題
5. 修復輸出過程自動下滑失效的問題
6. 優化窗口高度計算性能，提升移動端鍵盤彈出速度
7. 修復小螢幕下一些樣式問題

本次更新感謝 @xiaoxiaowesley, @chaoliu719, @Jesse205, @trrahul 的貢獻

### v1.12.3 - 2025.05.08

1. 修復移動端從 1.9.x 版本升級安裝，會導致數據遺失的問題
2. Mac 端不再使用 Ctrl 作為快捷功能鍵，而是使用 Command 鍵

### v1.12.2 - 2025.04.29

1. 修復初始化過程數據遷移性能問題

### v1.12.1 - 2025.04.28

1. 修復 Latex 渲染問題
2. 修復左邊欄頂部不能拖動的問題
3. 修復 ChatboxAI 錯誤信息展示
4. 增加初始化過程日志展示

### v1.12.0 - 2025.04.24

1. Chatbox AI 支援 Gemini 多模態輸出
2. 修復重新生成消息時，無法同步當前聯網開關狀態的問題
3. 美化桌面端主界面 UI，去除系統標題列
4. 優化移動端存儲性能
5. 導入備份現在會合併對話列表而不是覆蓋
6. 更新新建話題圖標

### v1.11.12 - 2025.04.15

1. 修復 Claude API Host 的問題

### v1.11.11 - 2025.04.15

1. 修復聯網問答功能的一個 bug

### v1.11.10 - 2025.04.15

1. 改進更新檢查體驗
2. 修復自動更新降級問題
3. 修復模型視覺能力檢查問題
4. 修復從搭檔創建會話後不會選中新會話的問題
5. 生成消息時，提交按鈕改為停止按鈕
6. 改進 API 錯誤信息顯示

### v1.11.8 - 2025.04.05

1. 修復自定義 Gemini API Host 的問題

### v1.11.7 - 2025.04.04

1. 支援 Gemini 多模態輸出
2. 支援更多訊息數量上下文限制
3. 思考內容自動折疊
4. 調整自動檢查更新頻率
5. 修復部分模型工具調用
6. 修復 ollama 圖片理解支援

### v1.11.5 - 2025.03.28

1. 修復 XAI 僅支持 Grok-Beta 模型問題，現可正常使用其他模型  
2. 修復部分模型在推理時未展示思考過程的問題  
3. 修復特定情況下應用無法正常關閉的錯誤  
4. 優化移動設備上的網路搜尋體驗，提高穩定性和流暢度  
5. 其他性能優化與問題修復

### v1.11.3 - 2025.03.24

1. 重構設置 UI
2. 修復了一些 LLM API 調用問題
3. 修復了模型溫度和 topP 參數問題

### v1.10.7 - 2025.03.17

1. 新增 beta 更新渠道，可在設定中開啟 beta 更新

### v1.10.5 - 2025.03.10

1. 改善模型設置 UI
2. 為 Azure OpenAI 添加 API 版本設置
3. 修復了一些消息格式問題
4. 改善網路搜尋切換 UI

### v1.10.4 - 2025.03.01

1. 改善網路搜尋功能

### v1.10.2 - 2025.02.28

1. 所有模型均已支援網路搜尋功能

### v1.10.1 - 2025.02.28

1. 修復網路問答功能的一個 bug

### v1.10.0 - 2025.02.24

1. 網路搜尋問答功能現支援任意 OpenAI 相容模型！此功能可直接使用，無需配置其他 API（僅桌面版本）

### v1.9.8 - 2025.02.06

1. 修復 o1 無法識別圖片的問題
2. 更新 Perplexity 模型列表，支援顯示思考鏈
3. 修復了其他顯示問題

### v1.9.7 - 2025.02.03

1. 優化 SiliconFlow 的 DeepSeek R1 模型的思考鏈顯示。  
2. 優化 LM Studio 的 DeepSeek R1 模型的思考鏈顯示。  
3. 增加消息中可附帶的檔案數量至 10 個。  
4. 支援接入 Azure 部署的 o1 和 o3 系列模型。  
5. 新增對 o3-mini 新模型的支援。  

### v1.9.5 - 2025.01.28

1. 修復零星問題

### v1.9.3 - 2025.01.26

1. 新增本地文件解析功能，現可將 PDF/DOC/PPT/XLSX 等檔案發送至任何模型 API
2. 新增摺疊 Ollama 部署的 DeepSeek-R1 模型思考過程功能
3. 修復思考過程換行顯示問題
4. 修復快速鍵設定相關問題
5. 修復其他零星問題

### v1.9.1 - 2025.01.20

1. 新增支援 DeepSeek R1 模型。
2. 啟用顯示模型的思考過程（若模型支援此功能）。
3. 修復因網路問題導致 Artifact 預覽失敗的問題。

### v1.9.0 - 2025.01.18

1. 新增 DeepSeek 作為模型提供方
2. 新增 SiliconFlow 作為模型提供方
3. 新增 LM Studio 作為模型提供方
4. 新增 xAI 作為模型提供方
5. 新增 Perplexity 作為模型提供方
6. 新增快捷鍵編輯功能，支持錄製與修改快捷鍵
7. 切換會話時會記住每個會話的滾動條位置
8. 新增關閉自動更新的按鈕
9. 當貼上的文本過長時，將以檔案形式插入（可在設定中關閉）
10. 按住 Shift 鍵並點擊刪除按鈕，可跳過確認直接刪除
11. 新增 Ctrl+E 快捷鍵以快速進入網路搜尋模式
12. 現在 Ctrl+Shift+V 可以貼上純文本
13. 匯入備份數據時，不會清理備份中不包含的數據
14. 每個 HTML 程式碼區塊現在均支持單獨的 artifact 預覽
15. 修復 SVG 在某些情況下無法顯示的問題
16. 修復 Latex 渲染問題，提升對各種異常情況的兼容性
17. 修復 Gemini 聯網問答時偶爾無法檢索網絡的問題
18. 優化了 Gemini 的系統提示效果
19. 修復其他一些小問題

### v1.8.1 - 2024.12.23

1. 修復 DALL-E 模型網路請求的 bug。

### v1.8.0 - 2024.12.22

1. 新增聯網問答功能。現在 Chatbox AI Models 和 Gemini 2.0 flash(API) 可以參考網際網路的即時資訊進行回答，並在訊息中列出參考資訊來源
2. 新增查看訊息生成時的首字延遲時間（first-token latency），可在設定中開啟
3. 新增傳送圖片的預處理能力。現在你可以傳送更多格式的圖片（例如 svg、gif），Chatbox 會自動轉化成模型可以接收的圖片格式，並且自動調節圖片大小以符合模型要求
4. 雙擊主題名稱可以快速彈出主題列表
5. 優化了自訂模型提供方的設定相容性，現在可以自動糾正和相容一些常見的設定錯誤
6. 溫度設定最大值調整為 2
7. 為所有刪除操作添加了二次確認
8. 更新模型列表，移除了一些棄用模型
9. 修復了其他一些小問題

### v1.7.0 - 2024.12.01

1. 新增訊息分支功能。訊息重新生成時將建立新的分支，分支之間可以左右切換。如果你希望保持原來的行為，你可以在分支選單中選擇展開所有分支的訊息。
2. 自動記住程式碼區塊的摺疊狀態
3. 優化 Markdown 和程式碼區塊的渲染效能
4. 支援將一個話題儲存為新會話
5. 可以在插入檔案或圖片時選擇多個檔案
6. 在 Windows 下全螢幕顯示時，滑鼠懸浮到標題列將顯示退出全螢幕的按鈕
7. 新增挪威語和瑞典語
8. 修復了一些其他問題

### v1.6.1 - 2024.11.12

1. 於自訂供應商的對話設定中，新增上下文數量及溫度值的設定選項。
2. 優化輸入框最大高度：當輸入內容較多時，輸入框會自動擴充至適當高度。
3. 為 mermaid 程式碼區塊新增複製按鈕功能。
4. 修正 mermaid 在繪製複雜流程圖時，文字顯示異常的問題。
5. 修正從 doc/ppt/xlsx/pdf 等文件複製內容時，僅能貼上圖片的問題。
6. 修正程式碼區塊下方的摺疊按鈕遮擋橫向捲軸的問題。

### v1.6.0 - 2024.11.03

1. 現在您可以傳送網頁連結了。Chatbox 會自動擷取網頁內容並將其納入聊天脈絡中。此功能適用於所有模型。
2. 現在您可以向任何模型傳送文字檔案。Chatbox 會在本地解析檔案內容並將其納入聊天脈絡中。
3. 新增了可摺疊的程式碼區塊功能。聊天記錄中較長的程式碼區塊會自動摺疊（可在設定中關閉此功能）。
4. 重新設計了圖片預覽視窗。
5. 重新設計了 SVG 圖片預覽和儲存功能。
6. 重新設計了 Mermaid 圖表預覽和儲存功能。
7. 改進了自動捲動行為：當生成的訊息填滿螢幕時，自動捲動會停止，以提升閱讀體驗。
8. 優化了整體應用程式的版面配置和間距。
9. 自動從遠端取得模型選項清單
10. 支援僅傳送附件（圖片/連結/檔案），無需輸入文字
11. 修復了自動生成標題時的語言偏好問題。
12. 修復了在複製的對話中編輯訊息時出現的資料問題。
13. 修復了阿拉伯語介面中抽屉方向的問題。

### v1.5.1 - 2024.10.09

1. 修復了 Windows 系統下可啟動多個應用實例，導致系統托盤出現重複圖標的問題
2. 在 MacOS 系統下，僅在手動開啟開機自啟動選項時才請求 System Events 權限，而非應用啟動時默認請求
3. 修復了自動生成的對話標題偶爾被截斷的問題


### v1.5.0 - 2024.10.05

1. 新增系統托盤/選單列的圖示常駐，優化顯示/隱藏快捷鍵
2. 輸入框現支援直接拖曳圖片或檔案插入
3. 新增開機自啟動選項（預設關閉，可在設定中手動開啟）
4. 新增話題標題選單，支援快速切換和刪除
5. 新增標題自動生成開關，關閉後不會自動生成標題以節省 token
6. 新增義大利語支援
7. Gemini 和 Groq 模型支援從遠端獲取最新模型列表
8. Gemini 模型預設支援發送圖片
9. Ollama 模型預設支援發送圖片
10. 更新了各廠商模型列表
11. 優化行動裝置體驗，修復鍵盤彈出後下方空隙過大的問題

### v1.4.2 - 2024.09.13

1. 新增模型 OpenAI o1 系列
2. 支持預覽 SVG 圖片
3. 修復了 Artifact 預覽功能在某些情況下無法正常顯示的問題
4. 修復了其他一些小問題

### v1.4.1 - 2024.09.04

1. 現在 Chatbox AI 服務的用戶可以選擇更多的模型
2. 新增阿拉伯語（العربية）
3. 優化了 LaTeX 的樣式
4. 修復了小機率情況下遺失資料的問題
5. 修復了其他一些小問題

### v1.4.0 - 2024.08.18

1. 新增 Mermaid 圖表預覽功能，現在可以預覽 AI 生成的圖表、流程圖、思維導圖等
2. 新增切換模型的快捷按鈕
3. 現在可以為自定義模型添加模型選項
4. 更新了各個廠商的模型列表
5. 連接 ollama 的 llava 模型時，支援發送圖片
6. 修改頭像後，支援回退到預設頭像
7. 新增對西班牙語和葡萄牙語的支援
8. 修復了一些已知的小問題

### v1.3.16 - 2024.07.22

1. 新增支援 gpt-4o-mini 模型 API

### v1.3.15 - 2024.07.17

1. 新增 Artifact 預覽功能，可以預覽生成消息中的 HTML 代碼（包括 JS/CSS/TailwindCSS 等）
2. Artifact 預覽功能支持彈窗模式打開
3. 在設置中新增自動渲染 Artifact 的選項開關
4. 改進了聊天 UI，更加美觀
5. 優化消息生成的性能
6. 修復了應用更新氣泡的已知問題
7. 修復了 Android 版本無法連接 Ollama 的問題
8. 兼容更低版本的 Android 系統
9. 修復了一些已知問題

### v1.3.14 - 2024.06.30

1. 可設置用戶和助手的頭像
2. 修復自定義提供方的一些 API 兼容問題
3. 修復了一些已知問題

### v1.3.13 - 2024.06.23

1. 新增支援 Claude 3.5 sonnet 模型
2. 修正一些已知問題

### v1.3.12 - 2024.06.11

1. 現在可以添加任意多的自訂模型提供方，方便接入及切換各種 API 兼容的新模型
2. 優化了移動端的鍵盤使用體驗
3. 修復了一些已知的小問題

### v1.3.11 - 2024.05.26

1. Chatbox AI 3.5 現在支持發送圖片。
2. 優化了搭檔列表的互動體驗。
3. 改進了夜間模式的代碼塊配色。

### v1.3.10 - 2024-05-15

1. 新增對 Gemini 1.5 Flash 模型的支援
1. 新增支援向 Gemini 模型系列傳送圖片功能

### v1.3.9 - 2024-05-14

1. 新增對 GPT-4o 模型的支援
2. 優化了全域訊息搜尋的使用者介面
3. 圖片詳情視窗現在支援縮放圖片
4. 優化了其他一些使用者介面細節

### v1.3.6 - 2024.05.04

**新功能：**
- 現在可以更便利地在會話專屬設定中修改系統提示。
- 支援導出會話的聊天記錄到 HTML、TXT、MD 等檔案格式。
- Custom Model 現在也支援發送圖片。
- 新增對 Groq 的支援。

**改進：**
- 自動摺疊過長的 system prompt，可手動展開以便查看完整內容。
- 重構了會話專屬設定的功能，新增了回退到全域設定的按鈕。
- 為了更佳的閱讀體驗，訊息正文調整了最大寬度，並支援點擊按鈕進行切換。
- 優化了懸浮菜單和懸浮按鈕組的顯示效能。

**修復：**
- 修復了創建 copilot 時無法輸入空格的問題。
- 修復了 GPT-4-Turbo 模型的 System Prompt 問題。
- 修復了當訊息高度很低時無法正常顯示訊息的問題。

**其他：**
- 切換歷史話題後，現在會自動滾動到訊息列表底部。
- Chatbox 網頁版本現在也能訪問本地 Ollama 服務。


### v1.3.5

1. 修正了從 Microsoft Word 複製內容時，文本錯誤地以圖像格式插入的問題。

### v1.3.4

1. 現在可以向 AI 發送檔案，支持包括 PDF、DOC、PPT、XLS、TXT 及程式碼等格式的檔案。
2. 輸入框支援插入系統剪貼簿中的圖片和檔案。
3. 新增支援自動生成話題標題的功能。
4. 加入支援最新的 gpt-4-turbo 模型。
5. Windows 版在安裝過程中支援修改安裝路徑。
6. Gemini 功能新增支援切換模型版本，包括更新支援 Gemini 1.5 pro 模型。
7. 修復了 Claude 中導致介面報錯的異常訊息序列問題。
8. 優化了部分使用者介面細節。

### v1.3.3

1. 您現在可以自訂消息中的使用者頭像。
2. 支援設定 Gemini 自定義 API 主機。
3. 提供設置選項，可選擇是否啟用 markdown 渲染與 latex 渲染。
4. 修正了 latex 渲染時出現的問題。
5. 修正訊息創建時出現的卡頓與崩潰的潛在問題。
6. 修正了自動更新時重複彈出窗口的問題。
7. 修正了其他一些細節問題。

### v1.3.1

1. 推出對 claude-3-sonnet-20240229 模型的支持。
2. 修正了導致在 claude 模型請求中出現空白文字區塊的錯誤。
3. 修正了在編輯過去信息時，信息列表中的自動滾動錯誤。

### v1.3.0

1. 新增支援向 AI 傳送圖片功能（Vision 功能）
2. 全面支持 Claude 3 系列模型
3. 全新重新設計應用程式布局
4. 新增訊息時間戳功能
5. 修復數個已知小問題

### v1.2.6

1. 新增 0125 版本系列模型 (gpt-3.5-turbo-0125, gpt-4-0125-preview, gpt-4-turbo-preview)
2. 優化一些窗口的移動端適配問題

### v1.2.4

1. 現在您可以使用⬆️⬇️方向鍵，來選擇和快速輸入過往的訊息
2. 修復了️拼字檢查功能，並可在設定中關閉此功能
3. 現在您從 Chatbox 複製的文字，將以純文字形式複製到剪貼簿（不再包含背景顏色，這個長久以來的小錯誤終於得到修復）

### v1.2.2

1. 新增話題歸檔（刷新上下文）、歷史話題列表等功能
2. 新增 Google Gemini 模型支持
3. 新增對 Ollama 支援，可輕鬆访问本地部署的 llama2、mistral、mixtral、codellama、vicuna、yi、solar 等模型
4. 修復全螢幕視窗第二次啟動時無法恢復全螢幕的問題

### v1.2.1

1. 重新設計訊息編輯視窗
2. 修復 token 配置無法儲存的問題
3. 修正複製後的新對話位置問題
4. 簡化設定中的提示語
5. 優化了一些互動問題
6. 修復了一些其他問題

### v1.2.0

- 新增圖片生成功能（Image Creator），現在您可以在 Chatbox 中生成圖片了。由 Dall-E-3 模型提供支持。
- 優化一些互動問題

### v1.1.4

- 新增對模型 gpt-3.5-turbo-1106、gpt-4-1106-preview 的直接支援
- 更新訊息 token 的計算方式，更加準確
- 新增 Top P 參數選項
- Temperature 參數支持小數點後兩位
- 軟體啟動時保持上次的會話

### v1.1.2

- 優化了搜索框的交互體驗
- 修復新消息的滾動問題
- 修復了一些網絡相關的問題

### v1.1.1

- 修復了在生成過程中無法選擇消息內容的問題
- 提升了搜索功能的性能，更快更準確
- 調整了訊息的排版樣式
- 修復了其他一些小問題

### v1.1.0

- 新增搜尋功能，能在當前會話或全部會話中搜尋訊息
- 支援資料備份與恢復（導入/導出）
- 修復了一些小問題

### v1.0.4

- 啟動後保持上一次的視窗大小與位置 (#30)
- 隱藏系統標籤菜單欄 (適用於 Windows、Linux)
- 修復會話專屬設置導致的授權和其他設置異常問題 (#31)
- 調整了一些使用者界面細節

### v1.0.2

- 引用訊息時光標自動移動至輸入框底部
- 修復切換模型時重置上下文長度設定的問題 #956
- 自動相容各種 Azure Endpoint 配置 #952

### v1.0.0

- 支援 OpenAI 自訂模型 (#28)
- 向上鍵可以快速輸入上一條發送的消息
- 新增 Windows、Linux 安裝包的 x64 和 arm64 架構版本
- 修復了 Azure OpenAI 無法修改模型部署名稱的問題（＃927）
- 修復了修改預設提示時無法輸入空格和換行的問題（＃942）
- 修復了編輯長消息後滾動條跳躍的問題
- 修復了其他一些小問題

### v0.6.7

- 當訊息列表滾動時，訊息的操作按鈕將會保持跟隨
- 新增對 Claude 系列模型的支援（beta 測試版）
- 支援更多國家語言
- 修復了一些小問題

### v0.6.5

- 新增應用程式快速鍵，可以透過快速鍵快速顯示/隱藏視窗、切換會話等，詳情請見設定頁面
- 新增對 OpenAI 0301、0314 系列模型的支援
- 新增了上下文訊息數量上限的設定，可以更靈活地控制上下文訊息數量，節省 token 消耗
- 對話設定中新增了溫度設定
- 修正了一些小問題

### v0.6.3

- 支援為每個對話調整模型設定（可以讓不同的對談使用不同的模型）
- 優化數據量較多時的性能瓶颈
- 讓 UI 更為緊湊
- 修復了一些小問題

### v0.6.2

- 新增對話列表批量清理功能
- 支援顯示訊息的 tokens 消耗
- 支援更改新會話的預設prompt提示
- 支援調整更小的字型大小
- 修正了其他一些小的問題

### v0.6.1

- 提升軟體的穩定性與運行效能
- 更友善的錯誤提示
- 起始時採用系統語言
- 修正 Windows 偶有的安裝錯誤、安裝白屏問題
- 修正 MacOS 10 關於設定儲存的相容問題
- 修正 Linux 下的運行效能問題
- 修正 API Host 在使用 HTTP 協議時的網路問題

### v0.5.6

- 優化了訊息上下文的視窗選擇策略
- 優化了訊息上下文與產生訊息 max tokens 的設定功能
- 修正了其他一些小的問題

### v0.5.2

- 修正 Windows 11 下的設定儲存問題
- 優化訊息產生的載入動畫
- 修正一些其他問題

### v0.5.1

- 修正 Windows 11 下的設定儲存問題

### v0.5.0

- 內建AI服務 “Chatbox AI”，開箱即用，直接高速訪問，再也不需要折騰網路、帳號和各種專業術語。
- 修正了重新啟動時夜間主題失效的問題
- 修正回答產生時無法切換會話的問題
- 修正訊息編輯時的卡頓問題
- 修正清理訊息後修改會話名稱、被清理訊息重新出現的問題
- 修正其他一些小的問題

### v0.4.5

- 新增 “AI 搭檔” 功能🚀🚀🚀
- 一大波聰明的 AI 搭檔已經準備好和你一起工作
- 你還可以透過 prompt 來創造自己的 AI 搭檔
- 新增對 ChatGLM-6B 的支援
- 修正一些已知的小問題

### v0.4.4

- 新增對 Microsoft Azure OpenAI API 的支援
- 修正一些已知的小問題
`

export default changelog
