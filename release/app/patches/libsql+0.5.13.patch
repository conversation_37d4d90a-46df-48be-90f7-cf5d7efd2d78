diff --git a/node_modules/libsql/index.js b/node_modules/libsql/index.js
index 9ac15cc..34554c8 100644
--- a/node_modules/libsql/index.js
+++ b/node_modules/libsql/index.js
@@ -23,7 +23,12 @@ function requireNative() {
   if (target === "linux-arm-gnueabihf" && familySync() == MUSL) {
       target = "linux-arm-musleabihf";
   }
-  return require(`@libsql/${target}`);
+  try {
+    return require(`@libsql/${target}`);
+  } catch (e) {
+    console.error("error loading libsql native module", e)
+    return {}
+  }
 }
 
 const {
diff --git a/node_modules/libsql/promise.js b/node_modules/libsql/promise.js
index 1fa6e67..93486c1 100644
--- a/node_modules/libsql/promise.js
+++ b/node_modules/libsql/promise.js
@@ -38,7 +38,16 @@ function requireNative() {
   if (target === "linux-arm-gnueabihf" && familySync() == MUSL) {
       target = "linux-arm-musleabihf";
   }
-  return require(`@libsql/${target}`);
+  if (target === "win32-arm64-msvc") {
+    // no native module for this target
+    return {}
+  }
+  try {
+    return require(`@libsql/${target}`);
+  } catch (e) {
+    console.error("error loading libsql native module", e)
+    return {}
+  }
 }
 
 const {
