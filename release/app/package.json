{"name": "xyz.chatboxapp.ce", "productName": "xyz.chatboxapp.ce", "version": "1.15.1", "description": "A desktop client for multiple cutting-edge AI models", "author": {"name": "Mediocre Company", "email": "<EMAIL>", "url": "https://github.com/chatboxai"}, "main": "./dist/main/main.js", "scripts": {"rebuild": "node -r ts-node/register ../../.erb/scripts/electron-rebuild.js", "postinstall": "patch-package && npm run rebuild && npm run link-modules", "link-modules": "node -r ts-node/register ../../.erb/scripts/link-modules.ts"}, "dependencies": {"@libsql/client": "^0.15.6"}, "devDependencies": {"patch-package": "^8.0.0"}}