{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] 保存, [Ctrl+Shift+Enter] 保存并发送", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[回车键] 发送，[Shift+回车键] 换行, [Ctrl+回车键] 发送但不生成", "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}": "{{count}} 个文件不支持：{{files}}。支持的格式：{{formats}}", "{{count}} MCP servers imported": "{{count}} 个 MCP 服务器已导入", "A provider with this ID already exists. Continuing will overwrite the existing configuration.": "此ID的提供方已存在。继续将覆盖现有配置。", "About": "关于", "About Chatbox": "版本信息", "about-introduction": "一个简约强大的 AI 桌面客户端，支持最先进的多款大语言模型，让前沿的人工智能技术变成易于使用的生产力工具。", "about-slogan": "用 AI 提高效率，工作学习的最佳拍档", "Access to all future premium feature updates": "享受未来所有专业功能的更新", "Action": "动作", "Activate License": "激活License", "Activating...": "激活中...", "Add": "添加", "Add at least one model to check connection": "添加至少一个模型以检查连接", "Add Custom Provider": "添加自定义提供方", "Add Custom Server": "添加自定义服务器", "Add File": "添加文件", "Add MCP Server": "添加 MCP Server", "Add or Import": "添加或导入", "Add provider": "添加模型提供方", "Add Server": "添加服务器", "Add your first MCP server": "添加您的第一个 MCP 服务器", "advanced": "其他", "Advanced": "高级", "Advanced Mode": "高级模式", "AI Model Provider": "AI 模型提供方", "ai provider no implemented paint tips": "当前 AI 模型提供方（{{aiProvider}}）暂时不支持绘图功能，目前仅 Chatbox AI、 OpenAI 与 Azure OpenAI 支持该功能，如有需要请<0>打开设置切换</0> AI 模型提供方", "AI Settings": "AI 设置", "AIHubMix integration in Chatbox offers 10% discount": "在Chatbox中接入AIHubMix可享受10%优惠", "All data is stored locally, ensuring privacy and rapid access": "所有数据都存储在本地，确保隐私和快速访问", "All major AI models in one subscription": "一个订阅高速访问所有主流 AI 模型", "All threads": "所有话题", "already existed": "已存在", "An easy-to-use AI client app": "一个简单易用的 AI 客户端应用", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "处理您的请求时发生错误。请稍后再试。如果此错误持续发生，请发送电子邮件至 <EMAIL> 以获得支持。", "An error occurred while sending the message.": "消息发送失败。", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "一个 MCP 服务器实现，提供一个工具，用于通过结构化思维过程进行动态和反思性问题解决。", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "发生未知错误。请稍后再试。如果此错误持续发生，请发送电子邮件至 <EMAIL> 以获得支持。", "any number key": "任何数字键", "api error tips": "遇到了来自 {{aiProvider}} 的错误，一般是由错误设置或账户问题引起的。请检查 AI 设置和账户情况，或者<0>点击这里查看常见问题文档</0>。", "api host": "API 域名", "API Host": "API 主机", "api key": "API 密钥", "API Key": "API 密钥", "API KEY & License": "API KEY & License", "API key invalid!": "API 密钥无效！", "API Key is required to check connection": "检查连接需要 API 密钥", "API Mode": "API 模式", "api path": "API 路径", "API Path": "API 路径", "Are you sure you want to delete the knowledge base": "你确定要删除知识库吗", "Are you sure you want to delete this server?": "你确定要删除这个服务器吗？", "Arguments": "参数", "assistant": "助手", "Attach Image": "添加图片", "Attach Link": "添加链接", "Auther Message": "“刚开始我只是想开发一个方便自己使用的小工具，没想到会有那么多人喜欢它！如果你愿意支持我的开发工作，可以适当进行捐赠，非常感谢。”", "Auto": "自动", "Auto (Use Chat Model)": "自动（使用对话模型）", "Auto (Use Chatbox AI)": "自动 (使用 Chatbox AI)", "Auto (Use Last Used)": "自动（使用上次使用的模型）", "Auto-collapse code blocks": "自动收起代码块", "Auto-Generate Chat Titles": "自动生成聊天标题", "Auto-preview artifacts": "自动预览生成物(Artifacts)", "Automatic updates": "自动更新", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "自动渲染生成物（例如，带有 CSS、JS、Tailwind 的 HTML）", "Azure API Key": "密钥", "Azure API Version": "Azure API 版本", "Azure Dall-E Deployment Name": "Dall-E 模型部署名称", "Azure Deployment Name": "模型部署名称", "Azure Endpoint": "Azure API 端点", "Back to Previous": "回到上个话题", "Beta updates": "Beta 更新", "Browsing and retrieving information from the internet.": "正在从互联网中浏览和检索信息。", "Builtin MCP Servers": "内置 MCP 服务器", "Can be activated on up to 5 devices": "最多可激活5台设备", "cancel": "取消", "Cancel": "取消", "cannot be empty": "不能为空", "Capabilities": "能力", "Changelog": "更新日志", "characters": "字符", "chat": "对话", "Chat": "聊天", "Chat History": "聊天记录", "Chat Settings": "对话设置", "Chatbox AI Advanced Model Quota": "Chatbox AI 高级模型配额", "Chatbox AI Cloud": "Chatbox AI 云", "Chatbox AI Image Quota": "Chatbox AI 图片剩余额度", "Chatbox AI License": "Chatbox AI 许可证", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI 旨在让更多人享受AI带来的工作效率提升，你只需按照成本价支付背后的模型成本费用", "Chatbox AI provides all the essential model support required for knowledge base processing": "Chatbox AI 提供知识库处理所需的所有基本模型支持", "Chatbox AI Quota": "Chatbox AI 配额", "Chatbox AI Standard Model Quota": "Chatbox AI 标准模型配额", "Chatbox Featured": "Chatbox精选", "Chatbox OCRs images with this model and sends the text to models without image support.": "Chatbox 使用此模型对图像进行 OCR 识别，并将文本发送给不支持图像的模型。", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox 尊重您的隐私，仅在必要时上传匿名错误数据和事件。您可以随时在设置中更改您的偏好。", "Chatbox will automatically use this model to construct search term.": "Chatbox 将自动使用此模型构建搜索词。", "Chatbox will automatically use this model to rename threads.": "Chatbox 将自动使用此模型重命名话题。", "Chatbox will use this model as the default for new chats.": "Chatbox 将使用此模型作为新对话的默认模型。", "ChatGLM-6B URL Helper": "支持开源模型 <1>ChatGLM-6B</1> 的 <0>API 接口</0>", "ChatGLM-6B Warnning for Chatbox-Web": "您似乎正在使用 Chatbox 网页版本，可能与 ChatGLM-6B 存在跨域等网络问题。建议下载 Chatbox 客户端来避免潜在问题。", "Check": "检查", "Check Update": "检查更新", "Child-inappropriate content": "儿童不宜内容", "Choose a file": "选择文件", "Choose a knowledge base": "选择知识库", "Chunk": "分块", "chunks": "分块", "clean": "清空", "clean it up": "清理", "Clear All Messages": "清空所有消息", "Clear Conversation List": "对话列表清理", "Click here to set up": "点击此处进行设置", "Click to view license details and quota usage": "点击查看 license 详情与配额使用情况", "close": "关闭", "Code Search": "代码搜索", "Collapse": "折叠", "Coming soon": "敬请期待", "Command": "命令", "Completed": "已完成", "Configuration Parsed Successfully": "配置解析成功", "Configure MCP server manually": "手动配置 MCP 服务器", "Confirm": "确认", "Confirm deletion?": "确认删除？", "Confirm to delete this custom provider?": "确认删除此自定义模型提供方？", "Confirm?": "确认吗？", "Connection failed!": "连接失败！", "Connection successful!": "连接成功！", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "连接 {{aiProvider}} 失败。这通常是由于配置错误或 {{aiProvider}} 账户问题。请<buttonOpenSettings>检查您的设置</buttonOpenSettings>并验证您的 {{aiProvider}} 账户状态，或购买<LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing>即可立即解锁所有高级模型，无需任何配置。", "Continue this thread": "继续该话题", "Continue this Thread": "继续该话题", "Conversation Settings": "对话设置", "copied to clipboard": "已复制到剪贴板", "Copilot Avatar URL": "搭档头像链接", "Copilot Name": "搭档名称", "Copilot Prompt": "人物设定（Prompt）", "Copilot Prompt Demo": "你是一个翻译员，你的工作是翻译中文到英文", "copy": "复制", "Copy reasoning content": "复制思考内容", "Create": "创建", "Create a New Conversation": "创建新的对话", "Create a New Image-Creator Conversation": "创建新的图片生成对话", "Create File": "创建文件", "Create First Knowledge Base": "创建首个知识库", "Create Knowledge Base": "创建知识库", "Create New Copilot": "创建新的AI搭档", "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.": "创建您的第一个知识库，开始添加文档，并通过上下文信息增强您的 AI 对话。", "creative": "想象发散", "Current conversation configured with specific model settings": "当前的对话配置了特定的模型设置", "Current model {{modelName}} does not support image input, using OCR to process images": "当前模型 {{modelName}} 不支持图像输入，正在使用 OCR 处理图像", "Current thread": "当前话题", "Custom": "自定义", "Custom MCP Servers": "自定义 MCP 服务器", "Custom Model": "自定义模型", "Custom Model Name": "自定义模型名", "Customize settings for the current conversation": "打开当前对话的专属设置", "Dark Mode": "深色模式", "Data Backup": "数据备份", "Data Backup and Restore": "数据备份与恢复", "Data Restore": "数据恢复", "Deactivate": "取消激活", "Deeply thought": "已深度思考", "Default Assistant Avatar": "默认助手头像", "Default Chat Model": "默认对话模型", "Default Models": "默认模型", "Default Prompt for New Conversation": "新对话的默认提示", "Default Settings for New Conversation": "新对话默认设置", "Default Thread Naming Model": "默认话题命名模型", "delete": "删除", "Delete": "删除", "delete confirmation": "此操作将永久删除 {{sessionName}} 的内容。您确定要继续吗？", "Delete Current Session": "删除当前会话", "Delete File": "删除文件", "Delete Knowledge Base": "删除知识库", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "部署 HTML 内容到 EdgeOne Pages 并获取一个可访问的公共 URL。", "Description": "描述", "Details": "详情", "Disabled": "禁用", "display": "显示", "Display": "显示", "Display Settings": "显示设置", "Documents": "文档", "Donate": "捐赠", "Done": "完成", "Download": "下载", "Drag and drop files here, or click to browse": "将文件拖放到此处，或点击浏览", "Drop files here": "将文件拖放到此处", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "由于本地处理的限制，建议使用<Link>Chatbox AI 服务</Link>以增强文档处理能力并获得更好的结果。", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "由于本地处理的限制，建议使用<Link>Chatbox AI 服务</Link>以增强网页解析能力，特别是动态网页。", "E-mail": "电子邮件", "e.g., Model Name, Current Date": "例如，模型名称、当前日期", "Easy Access": "轻松访问", "edit": "编辑", "Edit": "编辑", "Edit Avatars": "编辑头像", "Edit default assistant avatar": "编辑默认助手头像", "Edit File": "编辑文件", "Edit Knowledge Base": "编辑知识库", "Edit MCP Server": "编辑 MCP Server", "Edit Model": "编辑模型", "Edit Thread Name": "编辑话题名称", "Edit user avatar": "编辑用户头像", "Email Us": "邮件联系", "Embedding": "嵌入", "Embedding Model": "嵌入模型", "Enable optional anonymous reporting of crash and event data": "启用匿名崩溃和事件数据上报", "Enable Thinking": "启用思考", "Enabled": "已启用", "Ending with / ignores v1, ending with # forces use of input address": "以 / 结尾会忽略 v1，以 # 结尾强制使用输入地址", "Enjoying Chatbox?": "喜欢 Chatbox 吗？", "Enter": "回车键", "Environment Variables": "环境变量", "Error Reporting": "错误报告", "expand": "展开", "Expand": "展开", "Expansion Pack Quota": "扩展包配额", "Explore (community)": "探索 (社区)", "Explore (official)": "探索 (官方)", "export": "导出", "Export Chat": "导出聊天记录", "Export Selected Data": "导出勾选数据", "Exporting...": "正在导出...", "extension": "扩展", "Failed": "失败", "Failed to activate license, please check your license key and network connection": "激活许可证失败，请检查您的许可证密钥和网络连接", "Failed to create knowledge base, Error: {{error}}": "创建知识库失败，错误：{{error}}", "Failed to export file: {{error}}": "导出文件失败：{{error}}", "Failed to fetch Chatbox AI models config, Error: {{error}}": "获取 {{Chatbox}} {{AI}} 模型配置失败，错误：{{error}}", "Failed to fetch file chunks, Error: {{error}}": "获取文件分块失败，错误：{{error}}", "Failed to fetch files, Error: {{error}}": "获取文件失败，错误：{{error}}", "Failed to fetch knowledge base list, Error: {{error}}": "获取知识库列表失败，错误：{{error}}", "Failed to fetch models": "获取模型失败", "Failed to import provider": "导入提供方失败", "Failed to load Chatbox AI models configuration": "加载 Chatbox AI 模型配置失败", "Failed to open file dialog: {{error}}": "无法打开文件对话框：{{error}}", "Failed to read from clipboard": "无法从剪贴板读取", "Failed to save file: {{error}}": "保存文件失败: {{error}}", "Failed to update knowledge base, Error: {{error}}": "知识库更新失败，错误：{{error}}", "Failed to upload {{filename}}: {{error}}": "上传 {{filename}} 失败：{{error}}", "FAQs": "常见疑问", "Favorite": "收藏", "Feedback": "建议反馈", "Fetch": "获取", "File Chunks": "文件分块", "File Chunks Preview": "文件分块预览", "File saved to {{uri}}": "文件已保存到 {{uri}}", "File Search": "文件搜索", "File Size": "文件大小", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "不支持的文件类型。支持的类型包括 txt、md、html、doc、docx、pdf、excel、pptx、csv 以及所有文本文件，包括代码文件。", "Focus on the Input Box": "聚焦到输入框", "Focus on the Input Box and Enter Web Browsing Mode": "聚焦到输入框并进入联网问答模式", "Follow me on Twitter(X)": "关于我", "Follow System": "跟随系统", "Font Size": "字体大小", "font size changed, effective after next launch": "字体大小已改变，将在下次启动时生效", "Format": "格式", "Full-text search of chat history (coming soon)": "全文搜索聊天记录（敬请期待）", "Function": "功能", "General Settings": "常规设置", "Generate More Images Below": "在下方生成更多图片", "Get API Key": "获取 API 密钥", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "使用 Chatbox 桌面版获得更好的连接性和稳定性。<a>立即下载</a>。", "Get Files Meta": "获取文件元数据", "Get License": "获取License", "get more": "获取更多", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "有害或不适当的内容", "Hassle-free setup": "无需烦恼各种技术难题", "Hate speech or harassment": "仇恨言论或骚扰", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "在这里，您可以添加和管理各种自定义模型提供方。只要提供方的 API 与所选的 API 模式兼容，您就可以在 Chatbox 中无缝连接和使用它。", "Hide": "隐藏", "High": "高", "Home Page": "主页", "Homepage": "首页", "Hotkeys": "快捷键", "How to use?": "如何使用？", "ID": "ID", "Ideal for both work and educational scenarios": "适用于工作和教育场景", "Ideal for work and study": "适用于办公与学习场景", "Image Creator": "图片创建器", "Image Creator Intro": "Hi！我是 Chatbox Image Creator，“无情”的图片制造机。我可以根据你的描述生成精美图片，只要你能想象得到，我就能创造出来——迷人的风景、生动的角色、App 图标、或者抽象的构思……\n\n(๑•́ ₃ •̀๑) 额…我是一个有点自闭的机器人，所以**请直接告诉我你想要图片的文字描述**，我会集中我所有的像素去实现你的想象。\n\n现在请发挥你的想象力吧！", "Image Style": "图片风格", "Import and Restore": "导入与恢复", "Import Error": "导入错误", "Import failed, unsupported data format": "导入失败，数据格式不支持", "Import from clipboard": "从剪贴板导入", "Import from JSON in clipboard": "从剪贴板中的JSON导入", "Import MCP servers from JSON in your clipboard": "从您的剪贴板中的 JSON 导入 MCP 服务器", "Importing...": "正在导入...", "Improve Network Compatibility": "改善网络兼容性", "Inject default metadata": "注入默认元数据", "Insert a New Line into the Input Box": "在输入框中插入新行", "Instruction (System Prompt)": "系统提示（角色设定）", "Invalid deep link config format": "无效的 Deep Link 配置格式", "Invalid provider configuration format": "无效的提供方配置格式", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "检测到无效的请求参数。请稍后重试。如果多次失败可能表明当前软件版本过低，请升级以获得最新的功能与性能增强。", "It only takes a few seconds and helps a lot.": "只需几秒钟，并且非常有帮助。", "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest": "只保留列表中最顶部的 <0>{{N}}</0> 个对话，并永久删除其余", "Key Combination": "按键", "Keyboard Shortcuts": "键盘快捷键", "Knowledge Base": "知识库", "Knowledge Base Debug": "知识库调试", "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.": "由于库兼容性问题，知识库功能在 Windows ARM64 上不可用。此功能在 Windows x64、macOS 和 Linux 上可用。", "Language": "语言", "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.": "检测到大文件。为优化性能，将分批加载 {{count}} 个分块。", "Last Session": "上次会话", "LaTeX Rendering (Requires Markdown)": "LaTeX 渲染（需要 Markdown）", "Launch at system startup": "开机自启动", "License Activated": "License 已激活", "License expired, please check your license key": "license 已过期，请检查您的 license key", "License Expiry": "许可过期时间", "License not found, please check your license key": "未找到 license，请检查您的 license key", "License Plan Overview": "License 套餐概览", "lifetime license": "终身授权", "Light Mode": "浅色模式", "List Files": "列出文件", "Load More Chunks": "加载更多分块", "Loading chunks...": "加载分块中...", "Loading files...": "正在加载文件...", "Loading more chunks...": "正在加载更多分块...", "Loading webpage...": "加载网页中...", "Local (stdio)": "本地 (stdio)", "Local Mode": "本地模式", "Low": "低", "Make sure you have the following command installed:": "请确保您已安装以下命令：", "Manage License and Devices": "管理License与设备", "Manually": "手动", "Markdown Rendering": "Markdown 渲染", "Max Message Count in Context": "上下文的消息数量上限", "Max Output Tokens": "最大输出Token数", "max tokens in context": "上下文的最大Token数", "max tokens to generate": "生成回答的最大Token数", "Maybe Later": "稍后再说", "MCP server added": "MCP 服务器已添加", "MCP server for accessing arXiv papers": "MCP 服务器用于访问 arXiv 论文", "MCP Settings": "MCP 设置", "Medium": "中", "Mermaid Diagrams & Charts Rendering": "Mermaid 图表与图表渲染", "meticulous": "严谨细致", "MIME Type": "MIME 类型", "Misleading information": "误导信息", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "移动设备暂时不支持本地解析此文件类型。请使用文本文件（txt、markdown等）或使用<LinkToAdvancedFileProcessing>Chatbox AI 服务</LinkToAdvancedFileProcessing>进行云端文档分析。", "model": "模型", "Model": "模型", "Model ID": "模型ID", "Model Provider": "模型提供方", "Model Type": "模型类型", "Models": "模型", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "修改AI回复的创造力；值越高，回复变得越随机和有趣，而较低的值则确保更大的稳定性和可靠性。", "More Images": "更多图片", "Move to Conversations": "移动到对话", "My Assistant": "小助手", "My Copilots": "我的搭档", "name": "名称", "Name": "名称", "Name is required": "名称是必填的", "Natural": "写实", "Navigate to the Next Conversation": "跳转到下一个会话", "Navigate to the Next Option (in search dialog)": "导航到下一选项（在搜索弹窗中）", "Navigate to the Previous Conversation": "跳转到上一个会话", "Navigate to the Previous Option (in search dialog)": "导航到上一选项（在搜索弹窗中）", "Navigate to the Specific Conversation": "跳转到第N个会话", "network error tips": "网络错误。请检查当前的网络状态，以及与 {{host}} 的连接情况。", "Network Proxy": "网络代理", "network proxy error tips": "因为你设置了代理地址 {{proxy}}，请检查代理服务器是否正常工作，或者考虑在设置中删除代理地址。", "New": "新建", "New Chat": "新对话", "New Images": "新图片", "New knowledge base name": "新知识库名称", "New Thread": "新话题", "Nickname": "显示名称", "No": "否", "No chunks available. Try converting the file to a text format before adding it to the knowledge base.": "没有可用分块。请尝试将文件转换为文本格式，然后再添加到知识库。", "No documents yet": "暂无文档", "No eligible models available": "没有可用的合格模型", "No files were dropped": "未拖放任何文件", "No Knowledge Base Yet": "暂无知识库", "No Limit": "不限制", "No MCP servers parsed from clipboard": "未从剪贴板解析到MCP服务器", "No permission to write file": "没有权限写入文件", "No results found": "未找到任何结果", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "未找到搜索结果。请使用其他<OpenExtensionSettingButton>搜索提供方</OpenExtensionSettingButton>或稍后重试。", "None": "无", "not available in browser": "此功能在浏览器中无法使用，请下载桌面应用", "Not set": "未设置", "Nothing found...": "未找到...", "Number of Images per Reply": "每次回复的图片数量", "OCR Model": "OCR 模型", "One-click MCP servers for Chatbox AI subscribers": "Chatbox AI 订阅者的一键 MCP 服务器", "OpenAI API Compatible": "OpenAI API 兼容", "Operations": "操作", "optional": "可选", "or": "或", "Or become a sponsor": "或成为赞助商", "Other concerns": "其他问题", "Paste long text as a file": "粘贴长文本为文件", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "粘贴长文本时将以文件形式插入，有助于保持聊天列表简洁，并减少在 prompt caching 可用时大幅减少 token 使用量。", "Pause": "暂停", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, 代码...", "Pending": "待处理", "Platform Not Supported": "平台不支持", "Please describe the content you want to report (Optional)": "请描述您想要举报的内容（可选）", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "请确保远程 LM Studio 服务能够远程连接。更多详情请参考<a>此教程</a>。", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "请确保远程 Ollama 服务能够远程连接。更多详情请参考<a>此教程</a>。", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "请注意，作为客户端工具，Chatbox 无法保证模型提供方的服务质量和数据隐私。如果您正在寻找一个稳定、可靠、并且保护隐私的模型服务，请考虑<a>Chatbox AI</a>。", "Please select a model": "请选择模型", "Please test before saving": "保存前请测试", "Please wait about 20 seconds": "请等待约20秒", "pre-sale discount": "预售折扣", "premium": "专业版", "Premium Activation": "专业版激活", "Premium License Activated": "专业版已激活", "Premium License Key": "专业版授权激活码", "Press hotkey": "输入快捷键", "Preview": "预览", "Privacy Policy": "隐私政策", "Processing failed": "处理失败", "Processing...": "处理中...", "Prompt": "Prompt", "Provider Already Exists": "提供方已存在", "Provider configuration is valid and ready to import": "提供方配置有效并准备导入", "Provider Details": "提供方详情", "Provider not found": "未找到模型提供方", "Provider unavailable": "提供商不可用", "proxy": "代理", "Proxy Address": "代理地址", "Purchase": "购买", "QR Code": "二维码", "Query Knowledge Base": "查询知识库", "Quota Reset": "额度重置时间", "quote": "引用", "Rate Now": "立即评分", "Read File Chunks": "读取文件分块", "Reading file...": "正在读取文件...", "Reasoning": "推理", "RedNote": "小红书", "Refresh": "刷新", "regenerate": "重新生成", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "调整发送给AI的历史消息的数量，在理解深度和响应效率之间找到和谐的平衡。", "Remote (http/sse)": "远程 (http/sse)", "rename": "重命名", "Reply Again": "重新回答", "Reply Again Below": "在下方重新回答", "report": "举报", "Report Content": "举报内容", "Report Content ID": "举报内容 ID", "Report Type": "举报类型", "Rerank": "重排", "Rerank Model": "重排模型", "Rerank Model (optional)": "重排模型 (可选)", "reset": "重置", "Reset": "重置", "Reset All Hotkeys": "重置所有快捷键", "Reset to Default": "重置为默认值", "Reset to Global Settings": "重置为全局设置", "Result": "结果", "Resume": "继续", "Retrieve License": "找回License", "Retrieves up-to-date documentation and code examples for any library.": "检索任何库的最新文档和代码示例。", "Retry": "重试", "Roadmap": "未来规划", "save": "保存", "Save": "保存", "Save & Resend": "保存并发送", "Scope": "范围", "Search": "搜索", "Search All Conversations": "在所有对话中搜索", "Search in Current Conversation": "在当前对话中搜索", "Search models": "搜索模型", "Search Provider": "搜索提供方", "Search Providers": "搜索服务商", "Search query": "搜索查询", "Search Term Construction Model": "搜索词构建模型", "Search...": "搜索...", "Select and configure an AI model provider": "选择并配置 AI 模型提供方", "Select File": "选择文件", "Select Knowledge Base": "选择知识库", "Select Model": "选择模型", "Select the Current Option (in search dialog)": "选择当前选项（在搜索弹窗中）", "send": "发送", "Send": "发送", "Send Without Generating Response": "发送但不生成回答", "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.": "设置模型输出的最大 token 数量。请将其设置在模型可接受的范围内，否则可能会发生错误。", "Setting the avatar for Copilot": "设置 Copilot 搭档的头像", "settings": "设置", "Settings": "设置", "Sexual content": "色情内容", "Share File": "分享文件", "Share with Chatbox": "与Chatbox分享", "Show": "显示", "Show all ({{x}})": "显示全部 ({{x}})", "show first token latency": "显示首字耗时", "Show in Thread List": "在话题列表中显示", "show message timestamp": "显示消息的时间戳", "show message token count": "显示消息的 token 数量", "show message token usage": "显示消息的 token 消耗", "show message word count": "显示消息的字数统计", "show model name": "显示模型名称", "Show/Hide the Application Window": "显示/隐藏应用窗口", "Show/Hide the Search Dialog": "显示/隐藏搜索弹窗", "Showing {{loaded}} of {{total}} chunks": "显示 {{loaded}} / {{total}} 分块", "Showing first {{count}} chunks": "显示前 {{count}} 分块", "SiliconFlow": "硅基流动", "Smartest AI-Powered Services for Rapid Access": "最智能的 AI 服务，快速访问", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "抱歉，当前模型 {{model}} API 本身不支持图片理解。如果您需要发送图片，请切换到其他模型或使用推荐的 <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>。", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "抱歉，当前模型 {{model}} API 本身不支持图片理解。如果您需要发送图片，请切换到其他模型。", "Spam or advertising": "垃圾广告", "Special thanks to the following sponsors:": "特别鸣谢以下品牌的赞助:", "Specific model settings": "特定模型设置", "Spell Check": "拼写检查", "star": "星标", "Start a New Thread": "新话题", "Start Setup": "开始设置", "Startup Page": "启动页", "Status": "状态", "stop generating": "停止生成", "Stream output": "流式输出", "submit": "提交", "Successfully uploaded {{count}} file(s)": "成功上传 {{count}} 个文件", "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.": "已成功上传 {{success}}/{{total}} 个文件。{{failed}} 个文件上传失败。", "Support for ChatBox development": "支持ChatBox的开发", "Support jpg or png file smaller than 5MB": "支持小于 5MB 的 jpg 或 png 文件", "Supported formats": "支持的格式", "Supports a variety of advanced AI models": "支持多种先进的 AI 模型", "Survey": "调查问卷", "Switch": "切换", "system": "系统", "Tavily API Key": "Tavily API 密钥", "temperature": "严谨与想象(Temperature)", "Temperature": "温度", "Terminal": "终端", "Test": "测试", "Thank you for your report": "谢谢您的报告", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持文件。请下载<LinkToHomePage>桌面版应用</LinkToHomePage>来实现本地处理。", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持文件。请使用<LinkToAdvancedFileProcessing>Chatbox AI 模型</LinkToAdvancedFileProcessing>，或下载<LinkToHomePage>桌面版应用</LinkToHomePage>来实现本地处理。", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持链接。请下载<LinkToHomePage>桌面版应用</LinkToHomePage>来实现本地处理。", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持链接。请使用<LinkToAdvancedUrlProcessing>Chatbox AI 模型</LinkToAdvancedUrlProcessing>，或下载<LinkToHomePage>桌面版应用</LinkToHomePage>来实现本地处理。", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "当前模型 {{model}} API 不支持文档理解。您可以使用 <LinkToHomePage>Chatbox 桌面版</LinkToHomePage> 进行本地文档分析。", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "当前模型 {{model}} API 不支持文档理解。您可以使用 <LinkToAdvancedFileProcessing>Chatbox AI 服务</LinkToAdvancedFileProcessing> 进行云端文档分析，或下载 <LinkToHomePage>Chatbox 桌面版</LinkToHomePage> 进行本地文档分析。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "{{model}} API 本身不支持发送文件。由于本地文件解析的复杂性，Chatbox 只能处理基于文本的文件（包括代码）。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "{{model}} API 本身不支持发送文件。由于本地文件解析的复杂性，Chatbox 只能处理基于文本的文件（包括代码）。对于更多的文件格式和增强的文档理解能力，建议使用 <LinkToAdvancedFileProcessing>Chatbox AI 服务</LinkToAdvancedFileProcessing>。", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "当前模型 {{model}} API 本身不支持联网问答。支持的模型：{{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "当前模型 {{model}} API 本身不支持联网问答。支持的模型：<OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "未找到文件的缓存数据。请创建一个新的对话或刷新上下文，然后重新发送文件。", "The current model {{model}} does not support sending links.": "当前模型 {{model}} 不支持发送链接。", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "当前模型 {{model}} 不支持发送链接。目前支持的模型：Chatbox AI 模型。", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "文件大小超过了 50MB 的限制。请减小文件大小后重试。", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "您发送的文件已过期。为了保护您的隐私，所有与文件相关的缓存数据已被清除。您需要创建一个新的对话或刷新上下文，然后重新发送文件。", "The Image Creator plugin has been activated for the current conversation": "当前对话启动了 Image Creator 插件", "The license key you entered is invalid. Please check your license key and try again.": "您输入的 license key 无效。请检查您的 license key 并重试。", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "topP 参数控制 AI 响应的多样性：较低的值使输出更集中和可预测，而较高的值则允许更多样化和富有创意的回复。", "Theme": "主题", "Thinking": "思考中", "Thinking Budget": "思考预算", "Thinking Budget only works for 2.0 or later models": "思考预算仅适用于 2.0 及更高版本模型", "Thinking Budget only works for 3.7 or later models": "思考预算仅适用于 3.7 或更高版本模型", "Thinking Effort": "思考程度", "Thinking Effort only works for OpenAI o-series models": "思考仅适用于 OpenAI o 系列模型", "This action cannot be undone. All documents and their embeddings will be permanently deleted.": "此操作无法撤销。所有文档及其嵌入将被永久删除。", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "此 license 已达到激活上限，<a>点击这里</a>管理 license 与设备来取消激活旧设备。", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "这个服务器使LLMs能够从网页中检索和处理内容，将HTML转换为markdown以便更易于使用。", "Thread History": "历史话题", "To access locally deployed model services, please install the Chatbox desktop version": "为访问本地部署的模型服务，请安装 Chatbox 的桌面版本", "Toggle": "开关", "token": "Token", "Tool use": "工具使用", "Tool Use": "工具使用", "Tools": "工具", "Top P": "Top P", "Total Chunks": "总分块", "Type": "类型", "Type a command or search": "输入命令或搜索内容", "Type your question here...": "在这里输入你的问题...", "Unknown": "未知", "unknown error tips": "未知错误。请检查 AI 设置和账户情况，或者<0>点击这里查看常见问题文档</0>。", "Unlock Copilot Avatar by Upgrading to Premium Edition": "升级到专业版后解锁搭档头像", "Unsaved settings": "未保存的设置", "unstar": "取消星标", "Untitled": "未命名", "Update Available": "更新可用", "Upload failed: {{error}}": "上传失败：{{error}}", "Upload Image": "上传图片", "Upload your first document to get started": "上传您的第一个文档即可开始", "Upon import, changes will take effect immediately and existing data will be overwritten": "导入后将直接生效，原有数据将被覆盖", "Use My Own API Key / Local Model": "使用自己的 API Key 或本地模型", "Use proxy to resolve CORS and other network issues": "使用代理解决 CORS 和其他网络问题", "Used to extract text feature vectors, add in Settings - Provider - Model List": "用于提取文本特征向量，添加到设置 - 提供商 - 模型列表", "Used to get more accurate search results": "用于获取更准确的搜索结果", "Used to preprocess image files, requires models with vision capabilities enabled": "用于预处理图像文件，需要启用视觉能力的模型", "user": "用户", "User Avatar": "用户头像", "User Terms": "用户条款", "version": "版本", "View All Copilots": "查看所有搭档", "View historical threads": "查看历史话题", "View More Plans": "查看更多方案", "Violence or dangerous content": "暴力或危险内容", "Vision": "视觉", "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>": "视觉能力未在模型 {{model}} 上启用。请启用它或在<OpenSettingButton>Settings</OpenSettingButton>中设置默认的 OCR 模型。", "Vision Model": "视觉模型", "Vision Model (optional)": "视觉模型 (可选)", "Vision, Drawing, File Understanding and more": "视觉、绘图、文件理解等多种功能", "Vivid": "艺术", "VolcEngine": "火山引擎", "Web Browsing": "联网问答", "Web browsing (coming soon)": "联网回答 Web browsing（敬请期待）", "Web Browsing...": "联网搜索中...", "Web Search": "联网搜索", "WeChat": "微信", "What can I help you with today?": "今天我能为你提供什么帮助？", "Yes": "是", "You are already a Premium user": "你已经是专业版用户", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "您已超过 Chatbox AI 服务的速率限制。请稍后重试。", "You have no more Chatbox AI quota left this month.": "您本月的Chatbox AI配额已用尽。", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "您已经用完 {{model}} 模型的月度配额。请<OpenSettingButton>前往设置</OpenSettingButton>切换到其他模型，查看您的配额使用情况，或升级您的套餐。", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "您已选择 Chatbox AI 作为模型提供方，但尚未输入 license key。请<OpenSettingButton>点击这里打开设置</OpenSettingButton>并输入您的 license key，或选择其他模型提供方。", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "您已选择 Chatbox AI 作为搜索提供方，但尚未输入 license key。请<OpenSettingButton>点击这里打开设置</OpenSettingButton>并输入您的 license key，或选择其他<OpenExtensionSettingButton>搜索提供方</OpenExtensionSettingButton>。", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "您已选择 Tavily 作为搜索提供方，但尚未输入 API key。请<OpenExtensionSettingButton>点击这里打开设置</OpenExtensionSettingButton>并输入您的 API key，或选择其他搜索提供方。", "You have unsaved settings. Are you sure you want to leave?": "您有未保存的设置。确定要离开吗？", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "您的 ChatboxAI 订阅已包含来自各大供应商的模型访问权限。您可以直接在 ChatboxAI 中选择不同的模型，无需切换供应商。从 ChatboxAI 切换到其他供应商将需要他们各自的 API 密钥。<button>返回 ChatboxAI</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "您当前的 License（Chatbox AI Lite）不支持 {{model}} 模型。要使用此模型，请<OpenMorePlanButton>升级</OpenMorePlanButton>到 Chatbox AI Pro 或更高级别的套餐。或者，您可以通过<OpenSettingButton>访问设置</OpenSettingButton>切换到其他模型。", "Your license has expired. Please check your subscription or purchase a new one.": "您的 license 已过期。请检查您的订阅或重新购买。", "Your rating on the App Store would help make Chatbox even better!": "您的 App Store 评分将帮助 Chatbox 变得更好！"}