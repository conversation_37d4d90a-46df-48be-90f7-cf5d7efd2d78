const changelog = `
## v1.15.1 - 2025.07.22

1. 支持关闭流式输出
2. 支持设置 max token 参数
3. 修复移动端无法访问 ollama 的问题
4. 支持通过 deep link 一键导入 MCP 和提供方配置
5. 调整输入框样式
6. 修复新对话消息某些条件下没有被发出的问题

### v1.15.0 - 2025.07.07

1. 本地知识库支持
2. 调整思考和工具调用消息样式

### v1.14.4 - 2025.07.03

1. 修复对话列表中编辑功能会导致程序崩溃的问题

### v1.14.3 - 2025.06.28

1. 修复移动端导出数据会导致闪退的问题
2. 添加全局模型参数设置
3. 修复一些 markdown 和 Latex 的显示问题
4. 修复 OpenRouter 部分模型思考内容不展示的问题
5. 兼容 MCP 环境变量中包含=号字符的问题

本次更新感谢 @jakub-nezasa 的贡献

### v1.14.2 - 2025.06.19

1. 修复移动端换行会直接发送消息的问题
2. 修复部分设备上发送按钮失效的问题
3. 调整新话题按钮大小

### v1.14.1 - 2025.06.16

1. 修复重启后模型提供方设置丢失的问题

### v1.14.0 - 2025.06.16

1. 桌面端支持 MCP 
2. 全新首页设计
3. 添加火山引擎模型提供方
4. 修复 Azure 下自定义温度无效的问题，现在可以将o系列模型的温度设定为 1
5. 修复非 QWERT 键盘下快捷键错误

本次更新感谢 @Fr0benius 的贡献

### v1.13.4 - 2025.06.09

1. 修复存储性能问题
2. 修复英文语言下清理对话列表时，无法填写保留数字的问题

### v1.13.3 - 2025.06.08

1. 修复自定义提供方无法定制 API Path 的问题
2. OpenAI、Claude、Gemini 部分模型支持设置思考程度参数

### v1.13.2 - 2025.05.30

1. 修复窗口对话标题栏无法拖动的问题

### v1.13.1 - 2025.05.28

1. 大幅重构设置 UI
2. 在聊天会话中快速切换不同的模型提供商
3. 修复一个将话题移动到会话时，会复制原会话下所有话题的问题
4. 修复部分情况对话搜索无结果的问题
5. 修复输出过程自动下滑失效的问题
6. 优化窗口高度计算性能，提升移动端键盘弹出速度
7. 修复小屏幕下一些样式问题

本次更新感谢 @xiaoxiaowesley, @chaoliu719, @Jesse205, @trrahul 的贡献

### v1.12.3 - 2025.05.08

1. 修复移动端从 1.9.x 版本升级安装，会导致数据丢失的问题
2. Mac 端不再使用 Ctrl 作为快捷功能键，而是使用 Command 键

### v1.12.2 - 2025.04.29

1. 修复初始化过程数据迁移性能问题

### v1.12.1 - 2025.04.28

1. 修复 Latex 渲染问题
2. 修复左边栏顶部不能拖动的问题
3. 修复 ChatboxAI 错误信息展示
4. 增加初始化过程日志展示

### v1.12.0 - 2025.04.24

1. Chatbox AI 支持 Gemini 多模态输出
2. 修复重新生成消息时，无法同步当前联网开关状态的问题
3. 美化桌面端主界面UI，去除系统标题栏
4. 优化移动端存储性能
5. 导入备份现在会合并对话列表而不是覆盖
6. 更新新建话题图标

### v1.11.12 - 2025.04.15

1. 修复 Claude API Host 的问题

### v1.11.11 - 2025.04.15

1. 修复联网问答功能的一个 bug

### v1.11.10 - 2025.04.15

1. 改进更新检查体验
2. 修复自动更新降级问题
3. 修复模型视觉能力检查问题
4. 修复从搭档创建会话后不会选中新会话的问题
5. 生成消息时，提交按钮改为停止按钮
6. 改进 API 错误信息显示

### v1.11.8 - 2025.04.05

1. 修复自定义 Gemini API Host 的问题

### v1.11.7 - 2025.04.04

1. 支持 Gemini 多模态输出
2. 支持更多消息数量上下文限制
3. 思考内容自动折叠
4. 调整自动检查更新频率
5. 修复部分模型工具调用
6. 修复 ollama 图片理解支持

### v1.11.5 - 2025.03.28

1. 修复 XAI 仅支持 Grok-Beta 模型的问题，现可正常使用其他模型
2. 修复部分模型在推理时未展示思考过程的问题
3. 修复特定情况下应用无法正常关闭的错误
4. 优化移动设备上的联网搜索体验，提高稳定性和流畅度
5. 其他性能优化与问题修复

### v1.11.3 - 2025.03.24

1. 重构设置 UI
2. 修复了一些 LLM API 调用的问题
3. 修复了模型温度和 topP 参数的问题

### v1.10.7 - 2025.03.17

1. 新增 beta 更新渠道，可在设置中开启 beta 更新

### v1.10.5 - 2025.03.10

1. 改善模型设置 UI
2. 为 Azure OpenAI 添加 API 版本设置
3. 修复了一些消息格式问题
4. 改善联网搜索切换 UI

### v1.10.4 - 2025.03.01

1. 改善联网搜索功能

### v1.10.2 - 2025.02.28

1. 所有模型均已支持联网搜索功能

### v1.10.1 - 2025.02.28

1. 修复联网问答功能的一个 bug

### v1.10.0 - 2025.02.24

1. 联网搜索问答功能支持任意 OpenAI 兼容模型了！该功能可直接使用，无需配置其他API（仅桌面客户端）

### v1.9.8 - 2025.02.06

1. 修复 o1 无法识别图片的问题
2. 更新 Perplexity 的模型列表，支持显示思考链
3. 修复了其他一些显示问题

### v1.9.7 - 2025.02.03

1. 优化 SiliconFlow 的 DeepSeek R1 模型的思考链显示
2. 优化 LM Studio 的 DeepSeek R1 模型的思考链显示
3. 增加消息中可附带的文件数量到 10 个文件
4. 支持接入 Azure 部署的 o1、o3 系列模型
5. 新增对 o3-mini 新模型的支持

### v1.9.5 - 2025.01.28

1. 修复一些小问题

### v1.9.3 - 2025.01.26

1. 新增文档本地解析功能，现在可以发送 PDF/DOC/PPT/XLSX 等文件给任何模型 API
2. 可以折叠 ollama 部署的 deepseek-r1 模型的思考过程
3. 修复了思考过程的换行显示问题
4. 修复了快捷键设置导致的问题
5. 修复了其他一些小问题

### v1.9.1 - 2025.01.20

1. 新增 DeepSeek R1 模型支持
2. 新增模型推理过程的显示（如果模型支持）
3. 修复因网络问题导致的 Artifact 预览失败的问题

### v1.9.0 - 2025.01.18

1. 新增 DeepSeek 模型提供方
2. 新增 SiliconFlow 模型提供方
3. 新增 LM Studio 模型提供方
4. 新增 xAI 模型提供方
5. 新增 Perplexity 模型提供方
6. 新增快捷键修改功能，可以录制和修改快捷键
7. 切换会话时记住每个会话滚动条的位置
8. 新增软件自动更新的关闭按钮
9. 粘贴的文本过长时，将以文件的形式插入（可在设置中关闭）
10. 按住 Shift 键后点击删除按钮，可以跳过确认直接删除
11. 新增快捷键 Ctrl+E 快速进入联网问答模式
12. 现在 Ctrl+Shift+V 可以粘贴纯文本
13. 导入备份数据时，不清理备份中不包含的数据
14. 每个 html 代码块都单独支持 artifact 预览
15. 修复 SVG 在某些情况下无法显示的问题
16. 修复 Latex 渲染问题，兼容了各种异常问题
17. 修复 Gemini 联网问答时偶尔不检索网络的问题
18. 优化了 Gemini 的系统提示的效果
19. 修复了其他一些小问题

### v1.8.1 - 2024.12.23

1. 修复 DALL-E 模型网络请求的 bug。

### v1.8.0 - 2024.12.22

1. 新增联网问答功能。现在 Chatbox AI Models 和 Gemini 2.0 flash(API) 可以参考互联网的实时信息进行回答，并在消息中列出参考信息来源
2. 新增查看消息生成时的首字延迟时间（first-token latency），可在设置中开启
3. 新增发送图片的预处理能力。现在你可以发送更多格式的图片（例如 svg、gif），Chatbox 会自动转化成模型可以接收的图片格式，并且自动调节图片大小以符合模型要求
4. 双击话题名称可以快速弹出话题列表
5. 优化了自定义模型提供方的配置兼容性，现在可以自动纠正和兼容一些常见的配置错误
6. 温度设置最大值调整为 2
7. 为所有删除操作添加了二次确认
8. 更新模型列表，移除了一些弃用模型
9. 修复了其他一些小问题

### v1.7.0 - 2024.12.01

1. 新增消息分支功能。消息重新生成时将创建新的分支，分支之间可以左右切换。如果你希望保持原来的行为，你可以在分支菜单中选择展开所有分支的消息。
2. 自动记住代码块的折叠状态
3. 优化 Markdown 和代码块的渲染性能
4. 支持将一个话题保存为新会话
5. 可以在插入文件或图片时选择多个文件
6. 在 Windows 下全屏显示时，鼠标悬浮到标题栏将显示退出全屏的按钮
7. 新增挪威语和瑞典语
8. 修复了一些其他问题

### v1.6.1 - 2024.11.12

1. 在自定义提供方的会话设置中添加了上下文数量和温度设置。
2. 调整输入框的最大高度。当输入的内容较多时，输入框高度将自动变得更大。
3. 新增 mermaid 代码的复制按钮。
4. 修复了 mermaid 渲染复杂流程图时文字显示异常的问题。
5. 修复从doc/ppt/xlsx/pdf文件中复制内容时只插入图片的问题。
6. 修复代码块下方悬浮的折叠按钮遮挡横向滚动条的问题。

### v1.6.0 - 2024.11.03

1. 现在你可以发送网页链接。Chatbox 将自动获取网页内容并将其包含在聊天上下文中。适用于所有模型。
2. 现在你可以向任何模型发送文本文件。Chatbox 将在本地解析文件内容并将其包含在聊天上下文中。
3. 添加了可折叠的代码块。聊天历史中的长代码块会自动折叠（可在设置中禁用）。
4. 重新设计了图片预览窗口。
5. 重新设计了 SVG 图像预览和保存功能。
6. 重新设计了 Mermaid 图表预览和保存功能。
7. 改进了自动滚动行为：当生成的消息填满屏幕时，自动滚动将停止，以提高可读性。
8. 优化了整体应用布局和间距。
9. 自动从远程拉取模型选项列表
10. 支持不发送文本、只发送图片/链接/文件等附件
11. 修复了自动生成标题中的语言偏好问题。
12. 修复了在复制的对话中编辑消息时的数据问题。
13. 修复了阿拉伯语中抽屉方向的问题。

### v1.5.1 - 2024.10.09

1. 修复了 Windows 系统下可启动多个应用实例，导致系统托盘出现重复图标的问题
2. 在 MacOS 系统下，仅在手动开启开机自启动选项时才请求 System Events 权限，而非应用启动时默认请求
3. 修复了自动生成的对话标题偶尔被截断的问题

### v1.5.0 - 2024.10.05

1. 新增系统托盘/菜单栏的图标常驻，优化显示/隐藏快捷键
2. 输入框现支持直接拖拽图片或文件插入
3. 新增开机自启动选项（默认关闭，可在设置中手动开启）
4. 新增话题标题菜单，支持快速切换和删除
5. 新增标题自动生成开关，关闭后不会自动生成标题以节省 token
6. 新增意大利语支持
7. Gemini 和 Groq 模型支持从远程获取最新模型列表
8. Gemini 模型默认支持发送图片
9. Ollama 模型默认支持发送图片
10. 更新了各厂商模型列表
11. 优化移动端体验，修复键盘弹出后下方空隙过大的问题

### v1.4.2 - 2024.09.13

1. 新增模型 OpenAI o1 系列
2. 支持预览 SVG 图片
3. 修复了 Artifact 预览功能在某些情况下无法正常显示的问题
4. 修复了其他一些小问题

### v1.4.1 - 2024.09.04

1. 现在 Chatbox AI 服务的用户可以选择更多的模型
2. 新增阿拉伯语（العربية）
3. 优化了 latex 的样式
4. 修复了小概率情况下丢失数据的问题
5. 修复了其他一些小问题

### v1.4.0 - 2024.08.18

1. 新增 Mermaid 图表预览，现在可以预览 AI 生成的图表、流程图、思维导图等
2. 新增切换模型的快捷按钮
3. 现在可以为自定义模型添加模型选项
4. 更新了各个厂商的模型列表
5. 在连接 ollama 的 llava 模型时支持发送图片
6. 修改头像后，支持回退到默认头像
7. 新增了对西班牙语和葡萄牙语的支持
8. 修复了一些已知的小问题

### v1.3.16 - 2024.07.22

1. 添加对新模型 gpt-4o-mini 的 API 支持

### v1.3.15 - 2024.07.17

1. 新增 Artifact 预览功能，可以预览生成消息中的 HTML 代码（包括 JS/CSS/TailwindCSS 等）
2. Artifact 预览功能支持弹窗模式打开
3. 在设置中新增自动渲染 Artifact 的选项开关
4. 改进了聊天 UI，更加美观
5. 优化消息生成的性能
6. 修复了应用更新气泡的已知问题
7. 修复了 Android 版本无法连接 Ollama 的问题
8. 兼容更低版本的 Android 系统
9. 修复了一些已知问题

### v1.3.14 - 2024.06.30

1. 可以设置用户和助手的头像
2. 修复自定义提供方的一些 API 兼容问题
3. 修复了一些已知问题

### v1.3.13 - 2024.06.23

1. 添加对新模型 Claude 3.5 sonnet 的支持
2. 修复了一些已知问题

### v1.3.12 - 2024.06.11

1. 现在可以添加任意多的自定义模型提供方，可以方便地接入和切换各种 API 兼容的新模型
2. 优化了移动端的键盘使用体验
3. 修复了一些已知的小问题

### v1.3.11 - 2024.05.26

1. Chatbox AI 3.5 支持发送图片
2. 优化了搭档列表的交互体验
3. 改进了夜间模式的代码块配色

### v1.3.10 - 2024.05.15

1. 支持新模型 Gemini 1.5 Flash
2. 支持发送图片给 Gemini 模型系列

### v1.3.9 - 2024.05.14

1. 支持新模型 GPT-4o
2. 优化了全局消息搜索的 UI
3. 图片详情窗口支持缩放图片
4. 优化了其他一些 UI 细节

### v1.3.6 - 2024.05.04

**新功能:**
- 现在可以更方便地在会话专属设置中修改系统提示。
- 支持导出会话的聊天记录到 HTML、TXT、MD 等文件格式。
- Custom Model 现在也支持发送图片。
- 新增对 Groq 的支持。

**改进:**
- 自动折叠太长的 system prompt，可手动展开以便查看完整内容。
- 重构了会话专属设置的功能，新增了回退到全局设置的按钮。
- 为了更好的阅读体验，消息正文调整了最大宽度，并支持点击按钮进行切换。
- 优化了悬浮菜单和悬浮按钮组的显示性能。

**修复:**
- 修复了 GPT-4-Turbo 模型的 System Prompt 问题。
- 修复了当消息高度很低时无法正常显示消息的问题。
- 修复了创建 copilot 时无法输入空格的问题。

**其他:**
- 切换历史话题后，现在会自动滚动到消息列表底部。
- Chatbox 网页版本现在也能访问本地 Ollama 服务。

### v1.3.5

1. 修复了从 Microsoft Word 复制文本时错误地作为图像插入的问题。

### v1.3.4

1. 现在可以向 AI 发送文件，支持包括 PDF、DOC、PPT、XLS、TXT 及代码等格式的文件。
2. 输入框支持插入系统剪贴板中的图片和文件。
3. 新增支持自动生成话题标题的功能。
4. 加入支持最新的 gpt-4-turbo 模型。
5. Windows 版在安装过程中支持修改安装路径。
6. Gemini 功能新增支持切换模型版本，包括更新支持 Gemini 1.5 pro 模型。
7. 修复了 Claude 中导致接口报错的异常消息序列问题。
8. 优化了部分用户界面细节。

### v1.3.3

1. 现在你可以设置在消息中的用户头像
2. 支持设置 gemini 自定义 API Host
3. 支持在设置中选择是否启动 markdown 渲染和 latex 渲染
3. 修复 latex 的渲染问题
4. 修复消息生成时的卡顿和崩溃的潜在问题
5. 修复自动更新的重复弹窗问题
6. 修复其他一些小问题

### v1.3.1

1. 引入了对 claude-3-sonnet-20240229 模型的支持。
2. 修复了在 claude 模型请求中导致空白文本块出现的错误。
3. 修复了在编辑过去消息时，消息列表中的自动滚动错误。

### v1.3.0

1. 支持向 AI 发送图片（Vision 功能）
2. 支持 Claude 3 系列模型
3. 重新设计了整个应用的布局
4. 新增消息时间戳
5. 修复了一些已知的小问题

### v1.2.6

1. 新增 0125 版本系列模型 (gpt-3.5-turbo-0125, gpt-4-0125-preview, gpt-4-turbo-preview)
2. 优化一些窗口的移动端适配问题

### v1.2.4

1. 现在你可以在输入框中用⬆️⬇️键来选择和快速输入过往消息
2. 修复了拼写检查功能，而且可以在设置中关闭
3. 现在你从 Chatbox 复制的文本，将会以纯文本的形式复制到剪贴板（不再包含背景颜色，这个长期存在的小bug终于修复了）

### v1.2.2

1. 新增话题归档（刷新上下文）、历史话题列表等功能
2. 新增 Google Gemini 模型的支持
3. 新增对 Ollama 支持，可轻松访问本地部署的 llama2、mistral、mixtral、codellama、vicuna、yi、solar 等模型
4. 修复全屏窗口第二次启动时无法恢复全屏的问题

### v1.2.1

1. 重设计了消息编辑窗口
2. 修复了 token 配置无法保存的问题
3. 修复了复制后新对话框的位置问题
4. 简化了设置中的提示语
5. 优化了一些交互问题
6. 修复了一些其他问题

### v1.2.0

- 新增图片生成功能（Image Creator)，现在你可以在 Chatbox 中生成图片了。由 Dall-E-3 模型提供支持。
- 优化一些交互问题

### v1.1.4

- 新增 gpt-3.5-turbo-1106 和 gpt-4-1106-preview 模型选项
- 更新了消息 token 计算方法，提高了准确性
- 增加了 Top P 参数选项
- Temperature 参数现在支持到小数点后两位
- 软件启动时将恢复上一次的会话状态

### v1.1.2

- 优化了搜索框的交互体验
- 修复新消息的滚动问题
- 修复了一些网络相关的问题

### v1.1.1

- 修复了无法在生成过程中选择消息内容的问题
- 提升了搜索功能的性能，速度更快，准确度更高
- 优化了消息的排版设计
- 解决了其他一些次要的问题

### v1.1.0

- 新增搜索功能，现在你可以在当前会话或所有会话中搜索消息
- 支持数据备份与恢复（导入/导出）
- 修复了一些小问题

### v1.0.4

- 启动后保持上一次窗口的大小与位置 (#30)
- 隐藏系统标签菜单栏 (for Windows, Linux)
- 修复会话专属设置导致的 license 与其他设置的异常问题 (#31)
- 调整了一些 UI 细节

### v1.0.2

- 引用消息时光标自动移动到输入框底部
- 修复切换模型时重置上下文长度设置的问题 #956
- 自动兼容各种 Azure Endpoint 配置 #952

### v1.0.0

- 支持 OpenAI 自定义模型 (#28)
- 向上键可以快捷输入上一条发送的消息
- Windows、Linux 安装包新增 x64 和 arm64 架构版本
- 修复了会话设置中 Azure OpenAI 无法修改模型部署名的问题（#927）
- 修复了修改默认prompt时无法输入空格换行的问题 （#942）
- 修复了长消息点击编辑按钮后滚动条跳走的问题
- 修复了其他一些小问题

### v0.6.7

- 消息列表滚动时，消息的操作按钮保持跟随
- 新增对 Claude 系列模型的支持（beta）
- 支持更多国家语言
- 修复了一些小问题

### v0.6.5

- 新增应用快捷键，可以通过快捷键来快速显示/隐藏窗口、切换会话等，具体见设置页面
- 新增了上下文消息数量上限的设置，可以更加灵活地控制上下文消息数量，节省 token 消耗
- 添加了对 OpenAI 0301、0314 系列模型的支持
- 对话设置中新增了温度设置
- 修复了一些小问题

### v0.6.3

- 支持给每个对话修改模型设置（可以让不同的会话使用不同的模型）
- 优化数据较多时的性能瓶颈
- 让 UI 更加紧凑一些
- 修复了一些小问题

### v0.6.2

- 新增对话列表批量清理功能
- 支持显示消息的 tokens 消耗
- 支持修改新会话的默认prompt提示
- 支持修改更小的字体
- 修复了其他一些小的问题

### v0.6.1

- 提高软件的稳定性和运行性能
- 更友好的错误提示
- 初始化时使用系统语言
- 修复 Windows 偶现的安装错误、安装白屏问题
- 修复 MacOS 10 配置保存相关的兼容问题
- 修复 Linux 下的运行性能问题
- 修复 API Host 在使用 HTTP 协议时的网络问题

### v0.5.6

- 优化了消息上下文的窗口选择策略
- 优化了消息上下文与生成消息 max tokens 的设置功能
- 修复了其他一些小的问题

### v0.5.2

- 修复 Windows 11 下的设置保存问题
- 优化消息生成的加载动画
- 修复一些其他问题

### v0.5.1

- 修复 Windows 11 下的设置保存问题

### v0.5.0

- 内置AI服务 “Chatbox AI”，开箱即用，直接高速访问，再也不需要折腾网络、账户和各种技术术语。
- 修复了重新启动时夜间主题失效的问题
- 修复回答生成时无法切换会话的问题
- 修复消息编辑时的卡顿问题
- 修复清理消息后修改会话名称、被清理消息重新出现的问题
- 修复其他一些小的问题

### 0.4.5

- 新增 “AI 搭档” 功能🚀🚀🚀
- 一大波聪明的 AI 搭档已经准备好和你一起工作
- 你还可以通过 prompt 来创造自己的 AI 搭档
- 新增对 ChatGLM-6B 的支持
- 修复一些已知的小问题

### v0.4.4

- 新增对 Microsoft Azure OpenAI API 的支持
- 修复一些已知的小问题

`

export default changelog
