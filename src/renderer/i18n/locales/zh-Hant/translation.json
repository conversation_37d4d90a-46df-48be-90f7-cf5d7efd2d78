{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] 儲存, [Ctrl+Shift+Enter] 儲存後重發", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Enter] 傳送、[Shift＋Enter] 換行、[Ctrl＋Enter] 傳送但不產生", "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}": "{{count}} 檔案不支援：{{files}}。支援的格式：{{formats}}", "{{count}} MCP servers imported": "{{count}} MCP 伺服器已匯入", "A provider with this ID already exists. Continuing will overwrite the existing configuration.": "此 ID 的提供方已存在。繼續將覆蓋現有設定。", "About": "關於", "About Chatbox": "版本資訊", "about-introduction": "一個簡約強大的 AI 桌面客戶端，支援全球最先進的多款大型語言模型，讓前沿的人工智能技術成為易於使用的生產力工具。", "about-slogan": "使用 AI 提升效能，工作學習的最佳搭檔", "Access to all future premium feature updates": "享受未來所有專業功能的更新", "Action": "動作", "Activate License": "啟用License", "Activating...": "啟用中...", "Add": "添加", "Add at least one model to check connection": "新增至少一個模型以檢查連線", "Add Custom Provider": "添加自定義提供方", "Add Custom Server": "新增自訂伺服器", "Add File": "新增檔案", "Add MCP Server": "新增 MCP 伺服器", "Add or Import": "新增或匯入", "Add provider": "添加模型提供者", "Add Server": "新增伺服器", "Add your first MCP server": "新增您的第一個MCP伺服器", "advanced": "其他", "Advanced": "高級", "Advanced Mode": "高級模式", "AI Model Provider": "AI 模型提供者", "ai provider no implemented paint tips": "當前 AI 模型提供方（{{aiProvider}}）暫時不支持繪圖功能，目前僅 Chatbox AI、OpenAI 與 Azure OpenAI 支持該功能，如有需要請<0>開啟設置切換</0> AI 模型提供方", "AI Settings": "AI 設定", "AIHubMix integration in Chatbox offers 10% discount": "在Chatbox中接入AIHubMix可享受10%優惠", "All data is stored locally, ensuring privacy and rapid access": "所有數據都存儲在本地，確保隱私和快速訪問", "All major AI models in one subscription": "一個訂閱高速訪問所有主流 AI 模型", "All threads": "所有話題", "already existed": "已存在", "An easy-to-use AI client app": "一個簡單易用的 AI 客戶端應用程式", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "處理您的請求時發生錯誤。請稍後重試。如果此錯誤持續發生，請發送電子郵件至 <EMAIL> 以獲得支持。", "An error occurred while sending the message.": "傳送訊息時發生錯誤。", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "一個 MCP 伺服器實作，提供一個工具，透過結構化思考過程，實現動態和反思性問題解決。", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "發生未知錯誤。請稍後重試。如果此錯誤持續發生，請發送電子郵件至 <EMAIL> 以獲得支持。", "any number key": "任一數字鍵", "api error tips": "遇到了來自 {{aiProvider}} 的錯誤，一般是由錯誤設定或帳戶問題引起的。請檢查 AI 設定和帳戶狀況，或者<0>點擊這裡查看常見問題文件</0>。", "api host": "API 域名", "API Host": "API 主機", "api key": "API 金鑰", "API Key": "API 金鑰", "API KEY & License": "API KEY & License", "API key invalid!": "API 金鑰無效！", "API Key is required to check connection": "檢查連線需要 API 密鑰", "API Mode": "API 模式", "api path": "API 路徑", "API Path": "API 路徑", "Are you sure you want to delete the knowledge base": "您確定要刪除知識庫嗎", "Are you sure you want to delete this server?": "您確定要刪除這個伺服器嗎？", "Arguments": "參數", "assistant": "助理", "Attach Image": "附加圖片", "Attach Link": "添加鏈接", "Auther Message": "「剛開始我只是想開發一個方便自己使用的小工具，沒想到會有那麼多人喜歡它！如果你願意支持我的開發工作，可以適當進行捐贈，非常感謝。」", "Auto": "自動化", "Auto (Use Chat Model)": "自動（使用對話模型）", "Auto (Use Chatbox AI)": "自動 (使用 Chatbox AI)", "Auto (Use Last Used)": "自動（使用上次使用的模型）", "Auto-collapse code blocks": "自動收起代碼塊", "Auto-Generate Chat Titles": "自動生成聊天標題", "Auto-preview artifacts": "自動預覽生成內容(Artifacts)", "Automatic updates": "自動更新", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "自動渲染生成的內容（例如，帶有 CSS、JS、Tailwind 的 HTML）", "Azure API Key": "密鑰", "Azure API Version": "Azure API 版本", "Azure Dall-E Deployment Name": "Dall-E 模型部署名稱", "Azure Deployment Name": "模型部署名稱", "Azure Endpoint": "Azure API 端點", "Back to Previous": "回到上個話題", "Beta updates": "Beta 更新", "Browsing and retrieving information from the internet.": "正在從互聯網中瀏覽和檢索信息。", "Builtin MCP Servers": "內建 MCP 伺服器", "Can be activated on up to 5 devices": "最多可啟動5台設備", "cancel": "取消", "Cancel": "取消", "cannot be empty": "不能為空", "Capabilities": "能力", "Changelog": "變更紀錄", "characters": "字元", "chat": "對話", "Chat": "聊天", "Chat History": "聊天紀錄", "Chat Settings": "對話設定", "Chatbox AI Advanced Model Quota": "Chatbox AI 高級模型配額", "Chatbox AI Cloud": "Chatbox AI 雲端", "Chatbox AI Image Quota": "Chatbox AI 圖片餘額", "Chatbox AI License": "Chatbox AI 授權", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI 的目標是讓更多人體驗到 AI 帶來的工作效率提升，您只需支付相應的成本價格以應對模型的成本費用", "Chatbox AI provides all the essential model support required for knowledge base processing": "Chatbox AI 提供知識庫處理所需的所有必要模型支援", "Chatbox AI Quota": "Chatbox AI 配額", "Chatbox AI Standard Model Quota": "Chatbox AI 標準模型配額", "Chatbox Featured": "Chatbox精選", "Chatbox OCRs images with this model and sends the text to models without image support.": "Chatbox 會使用此模型進行圖片 OCR，並將文字傳送給不支援圖片的模型。", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox 尊重您的隱私，僅在必要時上傳匿名錯誤數據和事件。您可以隨時在設置中更改您的偏好。", "Chatbox will automatically use this model to construct search term.": "Chatbox 將自動使用此模型構建搜尋詞。", "Chatbox will automatically use this model to rename threads.": "Chatbox 將自動使用此模型重命名話題。", "Chatbox will use this model as the default for new chats.": "Chatbox 將使用此模型作為新對話的默認模型。", "ChatGLM-6B URL Helper": "支援開源模型 <1>ChatGLM-6B</1> 的 <0>API 介面</0>", "ChatGLM-6B Warnning for Chatbox-Web": "您似乎正在使用 Chatbox 網頁版本，可能與 ChatGLM-6B 存在跨域等網路問題。建議下載 Chatbox 客戶端以避免潛在問題", "Check": "檢查", "Check Update": "檢查更新", "Child-inappropriate content": "兒童不宜內容", "Choose a file": "選擇檔案", "Choose a knowledge base": "選擇知識庫", "Chunk": "區塊", "chunks": "區塊", "clean": "清除", "clean it up": "清理", "Clear All Messages": "清空所有消息", "Clear Conversation List": "清理對話列表", "Click here to set up": "按此設定", "Click to view license details and quota usage": "點擊查看 License 詳情與配額使用情況", "close": "關閉", "Code Search": "程式碼搜尋", "Collapse": "收起", "Coming soon": "請耐心等待喔", "Command": "指令", "Completed": "已完成", "Configuration Parsed Successfully": "設定解析成功", "Configure MCP server manually": "手動配置 MCP 伺服器", "Confirm": "確認", "Confirm deletion?": "確認刪除？", "Confirm to delete this custom provider?": "確認刪除此自定義提供者？", "Confirm?": "確認嗎？", "Connection failed!": "連接失敗！", "Connection successful!": "連接成功！", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "與 {{aiProvider}} 的連接失敗。這通常是由於配置不正確或 {{aiProvider}} 帳戶問題引起的。請<OpenSettingsButton>檢查您的設置</OpenSettingsButton>並驗證您的 {{aiProvider}} 帳戶狀態，或購買<LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing>即可立即解鎖所有高級模型，無需任何配置。", "Continue this thread": "繼續該話題", "Continue this Thread": "繼續該話題", "Conversation Settings": "對話設定", "copied to clipboard": "已複製到剪貼簿", "Copilot Avatar URL": "搭檔頭像鏈接", "Copilot Name": "搭檔名稱", "Copilot Prompt": "人物設定（Prompt）", "Copilot Prompt Demo": "你是一個翻譯員，你的工作是翻譯中文到英文", "copy": "複製", "Copy reasoning content": "複製推理內容", "Create": "建立", "Create a New Conversation": "創建新的聊天對話", "Create a New Image-Creator Conversation": "創建新的圖片製造機對話", "Create File": "建立檔案", "Create First Knowledge Base": "建立第一個知識庫", "Create Knowledge Base": "建立知識庫", "Create New Copilot": "創建新的AI搭檔", "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.": "建立您的第一個知識庫，以開始新增文件並透過上下文資訊增強您的 AI 對話。", "creative": "想像發散", "Current conversation configured with specific model settings": "當前的對話配置了特定模型設置", "Current model {{modelName}} does not support image input, using OCR to process images": "目前模型 {{modelName}} 不支援圖片輸入，將使用 OCR 處理圖片", "Current thread": "當前話題", "Custom": "自訂", "Custom MCP Servers": "自訂 MCP 伺服器", "Custom Model": "自訂模型", "Custom Model Name": "自訂模型名", "Customize settings for the current conversation": "為當前對話自定義設置", "Dark Mode": "深色模式", "Data Backup": "資料備份", "Data Backup and Restore": "資料備份與恢復", "Data Restore": "資料恢復", "Deactivate": "停用激活", "Deeply thought": "深思", "Default Assistant Avatar": "預設助理頭像", "Default Chat Model": "默認對話模型", "Default Models": "預設模型", "Default Prompt for New Conversation": "新對話的預設提示", "Default Settings for New Conversation": "新對話的預設設定", "Default Thread Naming Model": "預設話題命名模型", "delete": "刪除", "Delete": "刪除", "delete confirmation": "此動作將永久刪除 {{sessionName}} 的內容。您確定要繼續嗎？", "Delete Current Session": "刪除當前會話", "Delete File": "刪除檔案", "Delete Knowledge Base": "刪除知識庫", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "部署 HTML 內容到 EdgeOne Pages 並取得可存取的公開 URL。", "Description": "描述", "Details": "詳情", "Disabled": "已停用", "display": "顯示", "Display": "顯示", "Display Settings": "顯示設定", "Documents": "文件", "Donate": "捐贈", "Done": "完成", "Download": "下載", "Drag and drop files here, or click to browse": "拖曳檔案到此處，或點擊瀏覽", "Drop files here": "拖曳檔案到這裡", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "由於本地處理能力有限，建議使用 <Link>Chatbox AI 服務</Link>以獲得更強大的文件處理功能和更好的效果。", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "由於本地處理能力有限，建議使用 <Link>Chatbox AI 服務</Link>以增強網頁解析能力，特別是動態網頁。", "E-mail": "電子郵件", "e.g., Model Name, Current Date": "例如，模型名稱、當前日期", "Easy Access": "輕鬆訪問", "edit": "編輯", "Edit": "編輯", "Edit Avatars": "編輯頭像", "Edit default assistant avatar": "編輯預設助理頭像", "Edit File": "編輯檔案", "Edit Knowledge Base": "編輯知識庫", "Edit MCP Server": "編輯 MCP 伺服器", "Edit Model": "編輯模型", "Edit Thread Name": "編輯話題名稱", "Edit user avatar": "編輯用戶頭像", "Email Us": "發送郵件", "Embedding": "嵌入", "Embedding Model": "嵌入模型", "Enable optional anonymous reporting of crash and event data": "啟用選擇性的匿名崩潰和事件數據上報", "Enable Thinking": "啟用思考", "Enabled": "已啟用", "Ending with / ignores v1, ending with # forces use of input address": "以 / 結尾會忽略 v1，以 # 結尾強制使用輸入地址", "Enjoying Chatbox?": "喜歡 Chatbox 嗎？", "Enter": "回車", "Environment Variables": "環境變數", "Error Reporting": "錯誤上報", "expand": "展開", "Expand": "展開", "Expansion Pack Quota": "擴充包配額", "Explore (community)": "探索 (社群)", "Explore (official)": "探索 (official)", "export": "導出", "Export Chat": "導出聊天記錄", "Export Selected Data": "匯出選取的資料", "Exporting...": "正在匯出...", "extension": "擴展", "Failed": "失敗", "Failed to activate license, please check your license key and network connection": "激活授權失敗，請檢查您的授權金鑰和網路連接", "Failed to create knowledge base, Error: {{error}}": "建立知識庫失敗，錯誤：{{error}}", "Failed to export file: {{error}}": "匯出檔案失敗：{{error}}", "Failed to fetch Chatbox AI models config, Error: {{error}}": "無法取得 Chatbox AI 模型配置，錯誤：{{error}}", "Failed to fetch file chunks, Error: {{error}}": "無法取得檔案區塊，錯誤：{{error}}", "Failed to fetch files, Error: {{error}}": "無法獲取檔案，錯誤：{{error}}", "Failed to fetch knowledge base list, Error: {{error}}": "獲取知識庫列表失敗，錯誤：{{error}}", "Failed to fetch models": "獲取模型失敗", "Failed to import provider": "匯入提供方失敗", "Failed to load Chatbox AI models configuration": "載入 Chatbox AI 模型配置失敗", "Failed to open file dialog: {{error}}": "無法開啟檔案對話框：{{error}}", "Failed to read from clipboard": "從剪貼簿讀取失敗", "Failed to save file: {{error}}": "儲存檔案失敗：{{error}}", "Failed to update knowledge base, Error: {{error}}": "知識庫更新失敗，錯誤：{{error}}", "Failed to upload {{filename}}: {{error}}": "上傳 {{filename}} 失敗：{{error}}", "FAQs": "常見問題", "Favorite": "收藏", "Feedback": "建議回饋", "Fetch": "獲取", "File Chunks": "檔案區塊", "File Chunks Preview": "檔案區塊預覽", "File saved to {{uri}}": "檔案已儲存至 {{uri}}", "File Search": "檔案搜尋", "File Size": "檔案大小", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "不支持的文件類型。支持的類型包括 txt、md、html、doc、docx、pdf、excel、pptx、csv 和所有基於文本的文件，包括代碼文件。", "Focus on the Input Box": "聚焦到輸入框", "Focus on the Input Box and Enter Web Browsing Mode": "聚焦到輸入框並進入網路瀏覽模式", "Follow me on Twitter(X)": "關於我", "Follow System": "跟隨系統", "Font Size": "字型大小", "font size changed, effective after next launch": "字體大小已改變，將在下次啟動時生效", "Format": "格式", "Full-text search of chat history (coming soon)": "全文搜索聊天記錄（敬請期待）", "Function": "功能", "General Settings": "一般設定", "Generate More Images Below": "於下方生成更多圖片", "Get API Key": "獲取 API 密鑰", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "使用 Chatbox 桌面版獲得更好的連接性和穩定性。<a>立即下載</a>。", "Get Files Meta": "取得檔案中繼資料", "Get License": "获取License", "get more": "取得更多", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "有害或不適當的內容", "Hassle-free setup": "免去繁瑣技術問題的困擾", "Hate speech or harassment": "仇恨言論或騷擾", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "在這裡，您可以添加和管理各種自定義模型提供方。只要提供方的 API 與所選的 API 模式兼容，您就可以在 Chatbox 中無縫連接和使用它。", "Hide": "隱藏", "High": "高", "Home Page": "首頁", "Homepage": "首頁", "Hotkeys": "快捷鍵", "How to use?": "如何使用？", "ID": "ID", "Ideal for both work and educational scenarios": "適用於工作和教育場景", "Ideal for work and study": "適用於辦公與學習場景", "Image Creator": "圖片產生器", "Image Creator Intro": "Hi！我是 Chatbox Image Creator，圖片製造機。我可以根據你的描述生成精美圖片，只要你能想像得到，我就能創造出來——迷人的風景、生動的角色、App 圖示、或者抽象的構思……\n\n(๑•́ ₃ •̀๑) 呃…我是一個有點自閉的機器人，所以**請直接告訴我你想要圖片的文字描述**，我會集中我所有的像素去實現你的想像。\n\n現在請發揮你的想像力吧！", "Image Style": "圖片風格", "Import and Restore": "匯入與恢復", "Import Error": "匯入錯誤", "Import failed, unsupported data format": "匯入失敗，不支持的資料格式", "Import from clipboard": "從剪貼簿匯入", "Import from JSON in clipboard": "從剪貼簿中的 JSON 匯入", "Import MCP servers from JSON in your clipboard": "從您的剪貼簿中的 JSON 匯入 MCP 伺服器", "Importing...": "正在匯入...", "Improve Network Compatibility": "改善網路相容性", "Inject default metadata": "注入默認元數據", "Insert a New Line into the Input Box": "在輸入框中插入新行", "Instruction (System Prompt)": "指令（系統提示）", "Invalid deep link config format": "無效的 Deep Link 配置格式", "Invalid provider configuration format": "無效的提供方配置格式", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "檢測到無效的請求參數。請稍後重試。持續的失敗可能表明您的軟件版本過時。考慮升級以獲取最新的性能改進和功能。", "It only takes a few seconds and helps a lot.": "只需幾秒鐘，並且非常有幫助。", "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest": "保留列表中前 <0>{{N}}</0> 個對話，並永久刪除其餘對話", "Key Combination": "按鍵", "Keyboard Shortcuts": "鍵盤快捷鍵", "Knowledge Base": "知識庫", "Knowledge Base Debug": "知識庫除錯", "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.": "知識庫功能由於函式庫相容性問題，在 Windows ARM64 上不可用。此功能支援 Windows x64、macOS 和 Linux。", "Language": "語言", "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.": "偵測到大檔案。將以每次 {{count}} 個區塊分批載入，以優化效能。", "Last Session": "上次會話", "LaTeX Rendering (Requires Markdown)": "LaTeX 渲染（需要 Markdown）", "Launch at system startup": "開機自啟動", "License Activated": "License 已啟用", "License expired, please check your license key": "License 已過期，請檢查您的 License 密鑰", "License Expiry": "License 到期", "License not found, please check your license key": "找不到 License，請檢查您的 License 密鑰", "License Plan Overview": "License 套餐概覽", "lifetime license": "終身授權", "Light Mode": "淺色模式", "List Files": "列出檔案", "Load More Chunks": "載入更多區塊", "Loading chunks...": "載入區塊中...", "Loading files...": "正在載入檔案...", "Loading more chunks...": "載入更多區塊...", "Loading webpage...": "加載網頁中...", "Local (stdio)": "本機 (stdio)", "Local Mode": "本地模式", "Low": "低", "Make sure you have the following command installed:": "請確認您已安裝以下指令：", "Manage License and Devices": "管理License與設備", "Manually": "手動", "Markdown Rendering": "Markdown 渲染", "Max Message Count in Context": "上下文中的訊息數量上限", "Max Output Tokens": "最大輸出Token數", "max tokens in context": "上下文的Token上限", "max tokens to generate": "產生答案的Token上限", "Maybe Later": "稍後再說", "MCP server added": "MCP 伺服器已新增", "MCP server for accessing arXiv papers": "MCP 伺服器用於存取 arXiv 論文", "MCP Settings": "MCP 設定", "Medium": "中", "Mermaid Diagrams & Charts Rendering": "Mermaid 圖表與圖表渲染", "meticulous": "嚴謹細緻", "MIME Type": "MIME 類型", "Misleading information": "誤導信息", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "行動裝置暫時不支援此檔案類型的本機解析。請使用文字檔案 (txt、markdown 等)，或使用 <LinkToAdvancedFileProcessing>Chatbox AI 服務</LinkToAdvancedFileProcessing> 進行雲端文件分析。", "model": "模型", "Model": "模型", "Model ID": "模型 ID", "Model Provider": "模型提供方", "Model Type": "模型類型", "Models": "模型", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "修改AI回應的創造力；值越高，回應越隨機且有趣，而較低的值則確保更大的穩定性和可靠性。", "More Images": "更多圖片", "Move to Conversations": "移動到對話", "My Assistant": "小助手", "My Copilots": "我的搭檔", "name": "名稱", "Name": "名稱", "Name is required": "名稱為必填", "Natural": "寫實", "Navigate to the Next Conversation": "跳轉到下一個聊天對話", "Navigate to the Next Option (in search dialog)": "導航到下一選項（在搜尋對話框中）", "Navigate to the Previous Conversation": "跳轉到上一個聊天對話", "Navigate to the Previous Option (in search dialog)": "導航到上一選項（在搜尋對話框中）", "Navigate to the Specific Conversation": "跳轉到第N個聊天對話", "network error tips": "網路錯誤。請檢查目前的網路狀態，以及與 {{host}} 的連接情況。", "Network Proxy": "網路代理", "network proxy error tips": "因为你設置了代理位址{{proxy}}，請檢查代理伺服器是否正常運作，或者考慮在設定中刪除代理位址。", "New": "新建", "New Chat": "新對話", "New Images": "新圖片", "New knowledge base name": "新知識庫名稱", "New Thread": "新話題", "Nickname": "顯示名稱", "No": "否", "No chunks available. Try converting the file to a text format before adding it to the knowledge base.": "沒有可用的區塊。請嘗試將檔案轉換為文字格式，再將其新增至知識庫。", "No documents yet": "尚未有文件", "No eligible models available": "沒有可用的合格模型", "No files were dropped": "沒有檔案被拖放。", "No Knowledge Base Yet": "還沒有知識庫", "No Limit": "無限制", "No MCP servers parsed from clipboard": "沒有從剪貼簿解析到 MCP 伺服器", "No permission to write file": "沒有權限寫入檔案", "No results found": "未找到任何結果", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "未找到搜索結果。請使用其他<OpenExtensionSettingButton>搜索提供商</OpenExtensionSettingButton>或稍後重試。", "None": "無", "not available in browser": "此功能在瀏覽器中無法使用，請下載桌面應用程式以獲取全部功能。", "Not set": "未設定", "Nothing found...": "沒有找到...", "Number of Images per Reply": "每次回覆的圖片數量", "OCR Model": "OCR 模型", "One-click MCP servers for Chatbox AI subscribers": "一鍵 MCP 伺服器適用於 Chatbox AI 訂閱者", "OpenAI API Compatible": "OpenAI API 兼容", "Operations": "操作", "optional": "可選", "or": "或", "Or become a sponsor": "或成為贊助商", "Other concerns": "其他問題", "Paste long text as a file": "粘貼長文本為文件", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "粘貼長文本時將其作為文件插入，以保持聊天清潔並減少使用提示緩存時的令牌使用量。", "Pause": "暫停", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, Code...", "Pending": "待處理", "Platform Not Supported": "不支援此平台", "Please describe the content you want to report (Optional)": "請描述您想要舉報的內容（可選）", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "請確保遠程 LM Studio 服務能夠遠程連接。有關更多詳情，請參閱<a>此教程</a>。", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "請確保遠程 Ollama 服務能夠遠程連接。有關更多詳情，請參閱<a>此教程</a>。", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "請注意，作為客戶端工具，Chatbox 不能保證模型提供方的服務質量和數據隱私。如果您正在尋找穩定、可靠且保護隱私的模型服務，請考慮使用<a>Chatbox AI</a>。", "Please select a model": "請選擇模型", "Please test before saving": "請先測試再儲存", "Please wait about 20 seconds": "請等待約 20 秒", "pre-sale discount": "預售折扣", "premium": "高級版", "Premium Activation": "高級版啟用", "Premium License Activated": "高級版已啟用", "Premium License Key": "高級版授權密鑰", "Press hotkey": "輸入快捷鍵", "Preview": "預覽", "Privacy Policy": "隱私政策", "Processing failed": "處理失敗", "Processing...": "處理中...", "Prompt": "提示", "Provider Already Exists": "提供方已存在", "Provider configuration is valid and ready to import": "提供方設定有效並準備好匯入", "Provider Details": "提供方詳情", "Provider not found": "未找到模型提供者", "Provider unavailable": "供應商無法使用", "proxy": "代理", "Proxy Address": "代理位址", "Purchase": "購買", "QR Code": "QR 圖碼", "Query Knowledge Base": "查詢知識庫", "Quota Reset": "額度重置", "quote": "引用", "Rate Now": "立即評分", "Read File Chunks": "讀取檔案區塊", "Reading file...": "讀取文件中...", "Reasoning": "推理", "RedNote": "小紅書", "Refresh": "刷新", "regenerate": "重新產生", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "調節發送給AI的歷史訊息的數量，在理解深度和回應效率之間找到和諧的平衡。", "Remote (http/sse)": "遠端 (http/sse)", "rename": "改名", "Reply Again": "重新回覆", "Reply Again Below": "在下方重新回覆", "report": "舉報", "Report Content": "舉報內容", "Report Content ID": "舉報內容 ID", "Report Type": "舉報類型", "Rerank": "重排", "Rerank Model": "重排模型", "Rerank Model (optional)": "重排模型 (可選)", "reset": "重設", "Reset": "重置", "Reset All Hotkeys": "重置所有快捷鍵", "Reset to Default": "重置為預設值", "Reset to Global Settings": "重置為全局設置", "Result": "結果", "Resume": "繼續", "Retrieve License": "找回License", "Retrieves up-to-date documentation and code examples for any library.": "擷取任何函式庫的最新文件和程式碼範例。", "Retry": "重試", "Roadmap": "未來規劃", "save": "儲存", "Save": "儲存", "Save & Resend": "儲存並重發", "Scope": "範圍", "Search": "搜尋", "Search All Conversations": "搜尋所有對話", "Search in Current Conversation": "搜尋當前對話", "Search models": "搜尋模型", "Search Provider": "搜索提供商", "Search query": "查詢", "Search Term Construction Model": "搜尋詞構建模型", "Search...": "搜尋...", "Select and configure an AI model provider": "選擇並配置 AI 模型提供者", "Select File": "選擇文件", "Select Knowledge Base": "選擇知識庫", "Select Model": "選擇模型", "Select the Current Option (in search dialog)": "選擇當前選項（在搜尋對話框中）", "send": "傳送", "Send": "發送", "Send Without Generating Response": "發送但不產生回答", "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.": "設定模型輸出的最大 Token 數量。請將其設定在模型可接受的範圍內，否則可能會發生錯誤。", "Setting the avatar for Copilot": "設置 Copilot 搭檔的頭像", "settings": "設定", "Settings": "設定", "Sexual content": "色情內容", "Share File": "分享檔案", "Share with Chatbox": "與Chatbox分享", "Show": "顯示", "Show all ({{x}})": "顯示全部 ({{x}})", "show first token latency": "顯示首字耗時", "Show in Thread List": "在話題列表中顯示", "show message timestamp": "顯示消息的時間戳", "show message token count": "顯示消息的 token 數量", "show message token usage": "顯示消息的 token 消耗", "show message word count": "顯示消息的字數", "show model name": "顯示模型名稱", "Show/Hide the Application Window": "顯示/隱藏應用程式視窗", "Show/Hide the Search Dialog": "顯示/隱藏搜索對話框", "Showing {{loaded}} of {{total}} chunks": "顯示 {{loaded}} / 共 {{total}} 區塊", "Showing first {{count}} chunks": "顯示前 {{count}} 個區塊", "Smartest AI-Powered Services for Rapid Access": "最智能的 AI 服務，快速訪問", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "抱歉，當前模型 {{model}} API 本身不支持圖片理解。如果您需要發送圖片，請切換到其他模型或使用推薦的 <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>。", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "抱歉，當前模型 {{model}} API 本身不支持圖片理解。如果您需要發送圖片，請切換到其他模型。", "Spam or advertising": "垃圾廣告", "Special thanks to the following sponsors:": "特別感謝以下贊助商：", "Specific model settings": "具體模型設置", "Spell Check": "拼字檢查", "star": "星標", "Start a New Thread": "新話題", "Start Setup": "開始設置", "Startup Page": "啟動頁面", "Status": "狀態", "stop generating": "停止產生", "Stream output": "流式輸出", "submit": "提交", "Successfully uploaded {{count}} file(s)": "成功上傳 {{count}} 個檔案", "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.": "已成功上傳 {{success}} 個檔案，共 {{total}} 個。{{failed}} 個檔案上傳失敗。", "Support for ChatBox development": "支持ChatBox的開發", "Support jpg or png file smaller than 5MB": "支持小於 5MB 的 jpg 或 png 檔案", "Supported formats": "支援的格式", "Supports a variety of advanced AI models": "支持多種先進的 AI 模型", "Survey": "調查", "Switch": "切換", "system": "系統", "Tavily API Key": "Tavily API 密鑰", "temperature": "嚴謹與想像(Temperature)", "Temperature": "溫度", "Terminal": "終端機", "Test": "測試", "Thank you for your report": "感謝您的報告", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持文件。請下載 <LinkToHomePage>桌面版應用</LinkToHomePage>來實現本地處理。", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持文件。請使用 <LinkToAdvancedFileProcessing>Chatbox AI 模型</LinkToAdvancedFileProcessing>，或下載 <LinkToHomePage>桌面版應用</LinkToHomePage>來實現本地處理。", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持鏈接。請下載 <LinkToHomePage>桌面版應用</LinkToHomePage>來實現本地處理。", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API 不支持鏈接。請使用 <LinkToAdvancedUrlProcessing>Chatbox AI 模型</LinkToAdvancedUrlProcessing>，或下載 <LinkToHomePage>桌面版應用</LinkToHomePage>來實現本地處理。", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "當前模型 {{model}} API 不支持文檔理解。您可以使用 <LinkToHomePage>Chatbox 桌面版</LinkToHomePage> 進行本地文檔分析。", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "當前模型 {{model}} API 不支持文檔理解。您可以使用 <LinkToAdvancedFileProcessing>Chatbox AI 服務</LinkToAdvancedFileProcessing> 進行雲端文檔分析，或下載 <LinkToHomePage>Chatbox 桌面版</LinkToHomePage> 進行本地文檔分析。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "{{model}} API 本身不支持發送文件。由於本地文件解析的複雜性，Chatbox 只能處理基於文本的文件（包括代碼）。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "{{model}} API 本身不支持發送文件。由於本地文件解析的複雜性，Chatbox 只能處理基於文本的文件（包括代碼）。對於額外的文件格式和增強的文檔理解能力，建議使用 <LinkToAdvancedFileProcessing>Chatbox AI 服務</LinkToAdvancedFileProcessing>。", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "當前模型 {{model}} API 原本不支持網路瀏覽。支持的模型：{{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "當前模型 {{model}} API 原本不支持網路瀏覽。支持的模型：<OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "未找到文件的緩存數據。請創建一個新對話或刷新上下文，然後重新發送文件。", "The current model {{model}} does not support sending links.": "當前模型 {{model}} 不支持發送鏈接。", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "當前模型 {{model}} 不支持發送鏈接。目前支持的模型：Chatbox AI 模型。", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "文件大小超過 50MB 的限制。請減小文件大小並重試。", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "您發送的文件已過期。為了保護您的隱私，所有與文件相關的緩存數據已被清除。您需要創建一個新對話或刷新上下文，然後重新發送文件。", "The Image Creator plugin has been activated for the current conversation": "當前對話啟動了 Image Creator 插件", "The license key you entered is invalid. Please check your license key and try again.": "您輸入的授權密鑰無效。請檢查您的授權密鑰並重試。", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "topP 參數控制 AI 回應的多樣性：較低的值會使輸出更集中和可預測，而較高的值則允許更多樣和有創意的回覆。", "Theme": "主題", "Thinking": "思考中", "Thinking Budget": "思考預算", "Thinking Budget only works for 2.0 or later models": "思考預算僅適用於 2.0 或更新版本模型", "Thinking Budget only works for 3.7 or later models": "思考預算僅適用於 3.7 或更新的模型", "Thinking Effort": "思考程度", "Thinking Effort only works for OpenAI o-series models": "思考程度僅適用於 OpenAI o-series 模型", "This action cannot be undone. All documents and their embeddings will be permanently deleted.": "此動作無法復原。所有文件及其嵌入將被永久刪除。", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "此 license 授權密鑰已達到啟用限制，<a>點擊這裡</a>管理授權與設備以停用舊設備。", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "此伺服器讓大型語言模型能夠從網頁中擷取並處理內容，將 HTML 轉換為 Markdown 以便於閱讀。", "Thread History": "歷史話題", "To access locally deployed model services, please install the Chatbox desktop version": "為訪問本地部署的模型服務，請安裝 Chatbox 的桌面版本", "Toggle": "開關", "token": "Token", "Tool use": "工具使用", "Tool Use": "工具使用", "Tools": "工具", "Top P": "Top P", "Total Chunks": "總區塊", "Type": "類型", "Type a command or search": "輸入指令或搜索", "Type your question here...": "在這裡輸入你的問題...", "Unknown": "未知", "unknown error tips": "不明錯誤。請檢查 AI 設定和帳戶狀況，或者<0>點擊這裡查看常見問題文件</0>。", "Unlock Copilot Avatar by Upgrading to Premium Edition": "升級到高級版後解鎖搭檔頭像", "Unsaved settings": "未保存的設置", "unstar": "取消星標", "Untitled": "未命名", "Update Available": "更新可用", "Upload failed: {{error}}": "上傳失敗：{{error}}", "Upload Image": "上傳圖片", "Upload your first document to get started": "上傳您的首份文件即可開始", "Upon import, changes will take effect immediately and existing data will be overwritten": "匯入後將直接生效，原有資料將會被覆蓋", "Use My Own API Key / Local Model": "使用自己的 API Key 或本地模型", "Use proxy to resolve CORS and other network issues": "使用代理解決 CORS 和其他網路問題", "Used to extract text feature vectors, add in Settings - Provider - Model List": "用於提取文字特徵向量，在「設定」-「供應商」-「模型列表」中新增", "Used to get more accurate search results": "用於取得更精確的搜尋結果", "Used to preprocess image files, requires models with vision capabilities enabled": "用於預處理圖像文件，需要啟用視覺功能的模型", "user": "使用者", "User Avatar": "使用者頭像", "User Terms": "使用者條款", "version": "版本", "View All Copilots": "查看所有搭檔", "View historical threads": "查看歷史話題", "View More Plans": "瀏覽更多方案", "Violence or dangerous content": "暴力或危險內容", "Vision": "視覺", "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>": "模型 {{model}} 未啟用視覺能力。請啟用它，或在 <OpenSettingButton>設定</OpenSettingButton> 中設定預設的 OCR 模型。", "Vision Model": "視覺模型", "Vision Model (optional)": "視覺模型 (選填)", "Vision, Drawing, File Understanding and more": "視覺、繪圖、文件理解等", "Vivid": "藝術", "Web Browsing": "網路瀏覽", "Web browsing (coming soon)": "聯網回答 Web browsing（敬請期待）", "Web Browsing...": "網路瀏覽中...", "Web Search": "聯網搜索", "WeChat": "微信", "What can I help you with today?": "今天我能幫助你什麼？", "Yes": "是", "You are already a Premium user": "你已經是高級版用戶", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "您已超過 Chatbox AI 服務的速率限制。請稍後重試。", "You have no more Chatbox AI quota left this month.": "你本月的 Chatbox AI 配額已用完。", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "您已達到 {{model}} 模型的月配額。請<OpenSettingButton>前往設置</OpenSettingButton>切換到其他模型、查看您的配額使用情況，或升級您的方案。", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "您已選擇 Chatbox AI 作為模型提供者，但尚未輸入授權密鑰。請<OpenSettingButton>點擊這裡打開設置</OpenSettingButton>並輸入您的授權密鑰，或選擇其他模型提供者。", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "您已選擇 Chatbox AI 作為搜索提供商，但尚未輸入授權密鑰。請<OpenSettingButton>點擊這裡打開設置</OpenSettingButton>並輸入您的授權密鑰，或選擇其他<OpenExtensionSettingButton>搜索提供商</OpenExtensionSettingButton>。", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "您已選擇 Tavily 作為搜索提供商，但尚未輸入 API 密鑰。請<OpenExtensionSettingButton>點擊這裡打開設置</OpenExtensionSettingButton>並輸入您的 API 密鑰，或選擇其他搜索提供商。", "You have unsaved settings. Are you sure you want to leave?": "您有未保存的設置。確定要離開嗎？", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "您的 ChatboxAI 訂閱已包含來自各大供應商的模型訪問權限。您可以直接在 ChatboxAI 中選擇不同的模型，無需切換供應商。從 ChatboxAI 切換到其他供應商將需要他們各自的 API 金鑰。<button>返回 ChatboxAI</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "您當前的 License（Chatbox AI Lite）不支持 {{model}} 模型。要使用此模型，請<OpenMorePlanButton>升級</OpenMorePlanButton>到 Chatbox AI Pro 或更高級別的方案。或者，您可以通過<OpenSettingButton>訪問設置</OpenSettingButton>切換到其他模型。", "Your license has expired. Please check your subscription or purchase a new one.": "您的 License 已過期。請檢查您的訂閱或購買新的 License。", "Your rating on the App Store would help make Chatbox even better!": "您的 App Store 評分將幫助 Chatbox 變得更好！"}