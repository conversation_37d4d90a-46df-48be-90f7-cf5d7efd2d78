{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] Enregistrer, [Ctrl+Shift+Enter] Enregistrer et Renvoyer", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Entrée] envoyer, [<PERSON>+<PERSON><PERSON><PERSON>] saut de ligne, [Ctrl+Entrée] envoyer sans g<PERSON><PERSON>rer", "{{count}} file(s) not supported: {{files}}. Supported formats: {{formats}}": "{{count}} fichier(s) non pris en charge : {{files}}. Formats pris en charge : {{formats}}", "{{count}} MCP servers imported": "{{count}} serveurs MCP importés", "A provider with this ID already exists. Continuing will overwrite the existing configuration.": "Un fournisseur avec cet ID existe déjà. Continuer écrasera la configuration existante.", "About": "À propos", "About Chatbox": "À propos de Chatbox", "about-introduction": "Un client de bureau IA convivial prenant en charge plusieurs modèles IA avancés, transformant une technologie d'intelligence artificielle de pointe en un outil de productivité facile à utiliser.", "about-slogan": "Boostez votre efficacité avec l'IA, votre copilote ultime pour le travail et l'apprentissage", "Access to all future premium feature updates": "Accès à toutes les futures mises à jour des fonctionnalités premium", "Action": "Action", "Activate License": "Activer la <PERSON>", "Activating...": "Activation en cours...", "Add": "Ajouter", "Add at least one model to check connection": "Ajouter au moins un modèle pour vérifier la connexion", "Add Custom Provider": "Ajouter un Fournisseur Personnalisé", "Add Custom Server": "Ajouter un serveur personnalisé", "Add File": "Ajouter un fichier", "Add MCP Server": "Ajouter un serveur MCP", "Add or Import": "<PERSON><PERSON><PERSON> ou Importer", "Add provider": "Ajouter un fournisseur", "Add Server": "<PERSON><PERSON><PERSON>", "Add your first MCP server": "Ajouter votre premier serveur MCP", "advanced": "<PERSON><PERSON><PERSON>", "Advanced": "<PERSON><PERSON><PERSON>", "Advanced Mode": "Mode Avancé", "AI Model Provider": "Fournisseur de modèle IA", "ai provider no implemented paint tips": "Le fournisseur actuel du modèle AI ({{aiProvider}}) ne prend pas en charge la fonction de peinture pour le moment. Actuellement, seuls Chatbox AI, OpenAI et Azure OpenAI proposent cette fonctionnalité. Si nécessaire, veuillez <0>aller dans les paramètres</0> pour changer de fournisseur de modèle AI.", "AI Settings": "Paramètres de l'IA", "AIHubMix integration in Chatbox offers 10% discount": "L'intégration d'AIHubMix dans Chatbox offre une réduction de 10 %.", "All data is stored locally, ensuring privacy and rapid access": "Toutes les données sont stockées localement, garantissant la confidentialité et un accès rapide", "All major AI models in one subscription": "Tous les principaux modèles d'IA dans un abonnement", "All threads": "Tous les sujets", "already existed": "existe déj<PERSON>", "An easy-to-use AI client app": "Une application client IA facile à utiliser", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Une erreur s'est produite lors du traitement de votre requête. Veuillez réessayer plus tard. Si cette erreur persiste, veuillez envoyer un email à <EMAIL> pour obtenir de l'aide.", "An error occurred while sending the message.": "Une erreur est survenue lors de l'envoi du message.", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "Une implémentation de serveur MCP qui fournit un outil pour la résolution de problèmes dynamique et réflexive grâce à un processus de réflexion structuré.", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Une erreur inconnue s'est produite. Veuillez réessayer plus tard. Si cette erreur persiste, veuil<PERSON>z envoyer un email à <EMAIL> pour obtenir de l'aide.", "any number key": "n'importe quelle touche numérique", "api error tips": "Une erreur s'est produite avec {{aiProvider}}, qui est généralement causée par des paramètres incorrects ou des problèmes de compte. Veuillez vérifier vos paramètres d'IA et l'état de votre compte, ou <0>cliquez ici pour consulter le document FAQ</0>.", "api host": "Hôte API", "API Host": "Hôte API", "api key": "Clé API", "API Key": "Clé API", "API KEY & License": "API KEY & Licence", "API key invalid!": "Clé API invalide !", "API Key is required to check connection": "Clé API est requise pour vérifier la connexion", "API Mode": "Mode API", "api path": "Chemin de l'API", "API Path": "Chemin de l'API", "Are you sure you want to delete the knowledge base": "Voulez-vous vraiment supprimer la base de connaissances", "Are you sure you want to delete this server?": "Voulez-vous vraiment supprimer ce serveur ?", "Arguments": "Arguments", "assistant": "Assistant", "Attach Image": "Jo<PERSON><PERSON> une Image", "Attach Link": "Jo<PERSON>re un lien", "Auther Message": "J'ai créé Chatbox pour mon usage personnel et il est génial de voir autant de personnes l'apprécier ! Si vous souhaitez soutenir le développement, un don serait grandement apprécié, bien que cela soit entièrement facultatif. Un grand merci, <PERSON><PERSON>", "Auto": "Automatique", "Auto (Use Chat Model)": "Auto (Utiliser le modèle de discussion)", "Auto (Use Chatbox AI)": "Auto (Utiliser Chatbox AI)", "Auto (Use Last Used)": "Auto (Utiliser le dernier utilisé)", "Auto-collapse code blocks": "Réduire automatiquement les blocs de code", "Auto-Generate Chat Titles": "Auto-Générer les titres des conversations", "Auto-preview artifacts": "Aperçu automatique des artefacts", "Automatic updates": "Mises à jour automatiques", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "Rendu automatique des artefacts générés (par exemple, HTML avec CSS, JS, Tailwind)", "Azure API Key": "Clé API Azure", "Azure API Version": "Version de l'API Azure", "Azure Dall-E Deployment Name": "Nom du déploiement du modèle Azure Dall-E", "Azure Deployment Name": "Nom du déploiement Azure", "Azure Endpoint": "Point de terminaison Azure", "Back to Previous": "Retour au Sujet Précédent", "Beta updates": "<PERSON><PERSON> à jour bêta", "Browsing and retrieving information from the internet.": "Navigation web, recherche et récupération d'informations sur internet.", "Builtin MCP Servers": "Serveurs MCP Intégrés", "Can be activated on up to 5 devices": "Peut être activé sur jusqu'à 5 appareils", "cancel": "Annuler", "Cancel": "Annuler", "cannot be empty": "ne peut pas être vide", "Capabilities": "Capacités", "Changelog": "Journal des modifications", "characters": "caractères", "chat": "Discussion", "Chat": "Cha<PERSON>", "Chat History": "Historique de chat", "Chat Settings": "Paramètres de discussion", "Chatbox AI Advanced Model Quota": "Quota du modèle avancé Chatbox AI", "Chatbox AI Cloud": "Chatbox AI Cloud", "Chatbox AI Image Quota": "Quota d'Images Chatbox AI", "Chatbox AI License": "Licence Chatbox AI", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI propose une solution d'IA conviviale pour vous aider à améliorer votre productivité", "Chatbox AI provides all the essential model support required for knowledge base processing": "Chatbox AI fournit tout le support de modèle nécessaire au traitement de la base de connaissances", "Chatbox AI Quota": "Chatbox AI Quota", "Chatbox AI Standard Model Quota": "Quota du modèle standard Chatbox AI", "Chatbox Featured": "Chatbox en vedette", "Chatbox OCRs images with this model and sends the text to models without image support.": "Chatbox effectue l'OCR des images avec ce modèle et envoie le texte aux modèles sans prise en charge d'images.", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox respecte votre vie privée et ne télécharge que des données d'erreur anonymes et des événements lorsque cela est nécessaire. Vous pouvez modifier vos préférences à tout moment dans les paramètres.", "Chatbox will automatically use this model to construct search term.": "Chatbox utilisera automatiquement ce modèle pour construire le terme de recherche.", "Chatbox will automatically use this model to rename threads.": "Chatbox utilisera automatiquement ce modèle pour renommer les sujets.", "Chatbox will use this model as the default for new chats.": "Chatbox utilisera ce modèle par défaut pour les nouvelles discussions.", "ChatGLM-6B URL Helper": "Prend en charge l'<0>interface API</0> pour le modèle open-source, <1>ChatGLM-6B</1>", "ChatGLM-6B Warnning for Chatbox-Web": "Il semble que vous utilisiez la version Web de Chatbox, qui peut rencontrer des problèmes de domaine croisé ou d'autres problèmes de réseau avec ChatGLM-6B. Téléchargez et utilisez le client Chatbox pour éviter les problèmes potentiels.", "Check": "Vérifier", "Check Update": "Vérifier les mises à jour", "Child-inappropriate content": "Contenu inapproprié pour les enfants", "Choose a file": "<PERSON><PERSON> un fichier", "Choose a knowledge base": "Choisir une base de connaissances", "Chunk": "Segment", "chunks": "<PERSON><PERSON><PERSON><PERSON>", "clean": "<PERSON><PERSON><PERSON><PERSON>", "clean it up": "<PERSON><PERSON><PERSON><PERSON>", "Clear All Messages": "Effacer <PERSON> les Messages", "Clear Conversation List": "Effacer la liste des conversations", "Click here to set up": "Cliquez ici pour configurer", "Click to view license details and quota usage": "Cliquez pour consulter les détails de la licence et l'utilisation du quota", "close": "<PERSON><PERSON><PERSON>", "Code Search": "Recherche de code", "Collapse": "<PERSON><PERSON><PERSON><PERSON>", "Coming soon": "Bientôt disponible", "Command": "Commande", "Completed": "<PERSON><PERSON><PERSON><PERSON>", "Configuration Parsed Successfully": "Configuration analysée avec succès", "Configure MCP server manually": "Configurer le serveur MCP manuellement", "Confirm": "Confirmer", "Confirm deletion?": "Confirmer la suppression?", "Confirm to delete this custom provider?": "Confirmer la suppression de ce fournisseur personnalisé ?", "Confirm?": "Confirmer?", "Connection failed!": "Échec de la connexion !", "Connection successful!": "Connexion réussie !", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "La connexion à {{aiProvider}} a échoué. Cela se produit généralement en raison d'une configuration incorrecte ou de problèmes de compte {{aiProvider}}. Veuillez <buttonOpenSettings>vérifier vos paramètres</buttonOpenSettings> et vérifier le statut de votre compte {{aiProvider}}, ou achetez une <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> pour débloquer instantanément tous les modèles avancés sans aucune configuration.", "Continue this thread": "Continuer ce sujet", "Continue this Thread": "Continuer ce Sujet", "Conversation Settings": "Paramètres de la conversation", "copied to clipboard": "Copié dans le presse-papiers", "Copilot Avatar URL": "URL de l'avatar du copilote", "Copilot Name": "Nom du copilote", "Copilot Prompt": "Invite du copilote", "Copilot Prompt Demo": "Vous êtes un traducteur et votre travail consiste à traduire du non-anglais vers l'anglais", "copy": "<PERSON><PERSON><PERSON>", "Copy reasoning content": "<PERSON><PERSON><PERSON> le contenu du raisonnement", "Create": "<PERSON><PERSON><PERSON>", "Create a New Conversation": "<PERSON><PERSON><PERSON> une nouvelle conversation", "Create a New Image-Creator Conversation": "Créer une nouvelle conversation avec Image Creator", "Create File": "<PERSON><PERSON><PERSON> un fichier", "Create First Knowledge Base": "C<PERSON>er une première base de connaissances", "Create Knowledge Base": "<PERSON><PERSON>er une base de connaissances", "Create New Copilot": "C<PERSON>er un nouveau copilote", "Create your first knowledge base to start adding documents and enhance your AI conversations with contextual information.": "<PERSON><PERSON>ez votre première base de connaissances pour commencer à ajouter des documents et améliorer vos conversations {{AI}} avec des informations contextuelles.", "creative": "<PERSON><PERSON><PERSON><PERSON>", "Current conversation configured with specific model settings": "Conversation actuelle configurée avec des paramètres de modèle spécifiques", "Current model {{modelName}} does not support image input, using OCR to process images": "Le modèle actuel {{modelName}} ne prend pas en charge l'entrée d'image ; les images sont traitées par OCR", "Current thread": "Sujet actuel", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Custom MCP Servers": "Serveurs MCP Personnalisés", "Custom Model": "<PERSON><PERSON><PERSON><PERSON>", "Custom Model Name": "Nom du Modèle <PERSON>", "Customize settings for the current conversation": "Personnaliser les paramètres pour la conversation actuelle", "Dark Mode": "Mode Sombre", "Data Backup": "Sauvegarde de donn<PERSON>", "Data Backup and Restore": "Sauvegarde et restauration des données", "Data Restore": "Restauration de données", "Deactivate": "Désactiver", "Deeply thought": "Profondément réfléchi", "Default Assistant Avatar": "Avatar par défaut de l'assistant", "Default Chat Model": "Modèle de discussion par défaut", "Default Models": "Modèles par défaut", "Default Prompt for New Conversation": "Invite par défaut pour une nouvelle conversation", "Default Settings for New Conversation": "Paramètres par défaut pour Nouvelle conversation", "Default Thread Naming Model": "Mod<PERSON><PERSON> de nommage de sujet par défaut", "delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "delete confirmation": "Cette action supprimera définitivement tous les messages non système dans {{sessionName}}. Êtes-vous sûr de vouloir continuer ?", "Delete Current Session": "Supprimer la session actuelle", "Delete File": "<PERSON><PERSON><PERSON><PERSON> le fichier", "Delete Knowledge Base": "Supprimer la base de connaissances", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "Déployer du contenu HTML vers EdgeOne Pages et obtenir une URL publique accessible.", "Description": "Description", "Details": "Détails", "Disabled": "Désactivé", "display": "affichage", "Display": "Affichage", "Display Settings": "Paramètres d'affichage", "Documents": "Documents", "Donate": "Faire un don", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Télécharger", "Drag and drop files here, or click to browse": "Glis<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON> les fichiers ici, ou cliquez pour parcourir", "Drop files here": "Déposez les fichiers ici", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "Limité par le traitement local. Pour des résultats plus performants, passez à <Link>Chatbox AI Service</Link> pour le traitement avancé des documents.", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "Limité par le traitement local. Pour des résultats plus performants, passez à <Link>Chatbox AI Service</Link> pour le traitement avancé des documents, notamment pour les pages web dynamiques.", "E-mail": "E-mail", "e.g., Model Name, Current Date": "par exemple, <PERSON><PERSON> du Modèle, Date Actuelle", "Easy Access": "Accès facile", "edit": "Modifier", "Edit": "Modifier", "Edit Avatars": "Modifier les avatars", "Edit default assistant avatar": "Modifier l'avatar par défaut de l'assistant", "Edit File": "Modifier le fichier", "Edit Knowledge Base": "Modifier la base de connaissances", "Edit MCP Server": "Modifier le serveur MCP", "Edit Model": "Modifier le modèle", "Edit Thread Name": "Modifier le nom du fil de discussion", "Edit user avatar": "Modifier l'avatar de l'utilisateur", "Email Us": "Contact par e-mail", "Embedding": "Incorporation", "Embedding Model": "Mod<PERSON>le d'intégration", "Enable optional anonymous reporting of crash and event data": "Activer le signalement anonyme facultatif des données de plantage et d'événements", "Enable Thinking": "<PERSON>r la Réflexion", "Enabled": "Activé", "Ending with / ignores v1, ending with # forces use of input address": "Se terminant par / ignore v1, se terminant par # force l'utilisation de l'adresse d'entrée", "Enjoying Chatbox?": "Vous aimez Chatbox?", "Enter": "Entrée", "Environment Variables": "Variables d'environnement", "Error Reporting": "Signalement d'erreurs", "expand": "Développer", "Expand": "Développer", "Expansion Pack Quota": "Quota de packs d'extension", "Explore (community)": "Explorer (communauté)", "Explore (official)": "Explorer (officiel)", "export": "Exporter", "Export Chat": "Exporter la Discussion", "Export Selected Data": "Exporter les données sélectionnées", "Exporting...": "Exportation...", "extension": "Extensions", "Failed": "Échec", "Failed to activate license, please check your license key and network connection": "Échec de l'activation de la licence, veuillez vérifier votre clé de licence et votre connexion réseau", "Failed to create knowledge base, Error: {{error}}": "Échec de la création de la base de connaissances, Erreur : {{error}}", "Failed to export file: {{error}}": "Échec de l'exportation du fichier : {{error}}", "Failed to fetch Chatbox AI models config, Error: {{error}}": "Échec de la récupération de la configuration des modèles Chatbox AI, Erreur : {{error}}", "Failed to fetch file chunks, Error: {{error}}": "Échec de la récupération des blocs de fichier, Erreur : {{error}}", "Failed to fetch files, Error: {{error}}": "Échec de la récupération des fichiers, Erreur : {{error}}", "Failed to fetch knowledge base list, Error: {{error}}": "Échec de la récupération de la liste des bases de connaissances, Erreur : {{error}}", "Failed to fetch models": "Échec de la récupération des modèles", "Failed to import provider": "Échec de l'importation du fournisseur", "Failed to load Chatbox AI models configuration": "Échec du chargement de la configuration des modèles AI de Chatbox", "Failed to open file dialog: {{error}}": "Échec de l'ouverture de la boîte de dialogue de fichi<PERSON> : {{error}}", "Failed to read from clipboard": "Impossible de lire du presse-papiers", "Failed to save file: {{error}}": "Échec de l'enregistrement du fichier : {{error}}", "Failed to update knowledge base, Error: {{error}}": "Échec de la mise à jour de la base de connaissances, Erreur : {{error}}", "Failed to upload {{filename}}: {{error}}": "Échec du téléchargement de {{filename}} : {{error}}", "FAQs": "FAQ", "Favorite": "<PERSON><PERSON><PERSON>", "Feedback": "Commentaires", "Fetch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "File Chunks": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON>", "File Chunks Preview": "Aperçu des morceaux de fichier", "File saved to {{uri}}": "<PERSON><PERSON><PERSON> enregis<PERSON>é dans {{uri}}", "File Search": "Recherche de fichiers", "File Size": "<PERSON><PERSON>", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "Type de fichier non pris en charge. Les types pris en charge incluent txt, md, html, doc, docx, pdf, excel, pptx, csv et tous les fichiers basés sur du texte, y compris les fichiers de code.", "Focus on the Input Box": "Se concentrer sur la zone de saisie", "Focus on the Input Box and Enter Web Browsing Mode": "Focaliser sur la zone de saisie et entrer dans le mode de navigation web", "Follow me on Twitter(X)": "Suivez-moi sur Twitter(X)", "Follow System": "Su<PERSON>re le Système", "Font Size": "Taille de la police", "font size changed, effective after next launch": "La taille de la police a été modifiée, elle sera effective après le prochain lancement", "Format": "Format", "Full-text search of chat history (coming soon)": "Recherche en texte intégral de l'historique des discussions (bientôt disponible)", "Function": "Fonction", "General Settings": "Paramètres généraux", "Generate More Images Below": "G<PERSON><PERSON>rez plus d'images ci-dessous", "Get API Key": "Obtenir une clé API", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "Obtenez une meilleure connectivité et une stabilité avec l'application Chatbox pour bureau. <a>Télécharger maintenant</a>.", "Get Files Meta": "Obtenir les métas des fichiers", "Get License": "Obtenir une licence", "get more": "Obtenir plus", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "Contenu nocif ou offensant", "Hassle-free setup": "Configuration sans problème", "Hate speech or harassment": "Discours ou harc<PERSON><PERSON> ha<PERSON>", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "<PERSON><PERSON>, vous pouvez ajouter et gérer divers fournisseurs de modèles personnalisés. Tant que l'API du fournisseur est compatible avec le mode API sélectionné, vous pouvez vous connecter et l'utiliser de manière transparente dans Chatbox.", "Hide": "Masquer", "High": "<PERSON><PERSON><PERSON>", "Home Page": "Page d'accueil", "Homepage": "Page d'accueil", "Hotkeys": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "How to use?": "Comment utiliser ?", "ID": "Identifiant", "Ideal for both work and educational scenarios": "Idéal pour les scénarios de travail et d'éducation", "Ideal for work and study": "Idéal pour le travail et les études", "Image Creator": "Créateur d'images", "Image Creator Intro": "Bonjour ! Je suis Chatbox Image Creator, votre compagnon artistique IA dédié à transformer vos paroles en visuels frappants. Si vous pouvez le rêver, je peux le créer — des paysages enchanteurs, des personnages dynamiques, des icônes d'application jusqu'à l'abstrait et au-delà.\n\nJe suis un robot silencieux, il suffit **de me dire simplement la description de l'image que vous avez en tête**, et je concentrerai tous mes pixels pour concrétiser votre vision.\n\nFaisons de l'art !", "Image Style": "Style d'Image", "Import and Restore": "Importer et restaurer", "Import Error": "Erreur d'importation", "Import failed, unsupported data format": "Échec de l'importation, format de données non supporté", "Import from clipboard": "Importer depuis le presse-papiers", "Import from JSON in clipboard": "Importer depuis JSON dans le presse-papiers", "Import MCP servers from JSON in your clipboard": "Importer des serveurs MCP depuis JSON dans votre presse-papiers", "Importing...": "Importation...", "Improve Network Compatibility": "Améliorer la compatibilité du réseau", "Inject default metadata": "Injecter les métadonnées par défaut", "Insert a New Line into the Input Box": "Insérer une nouvelle ligne dans la zone de saisie", "Instruction (System Prompt)": "Instruction (Invite Système)", "Invalid deep link config format": "Format de configuration Deep Link invalide", "Invalid provider configuration format": "Format de configuration du fournisseur invalide", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "Des paramètres de requête invalides ont été détectés. Veuillez réessayer plus tard. Des échecs persistants peuvent indiquer une version obsolète du logiciel. Envisagez de mettre à niveau pour accéder aux dernières améliorations de performances et fonctionnalités.", "It only takes a few seconds and helps a lot.": "Cela ne prend que quelques secondes et est très utile.", "Keep only the Top <0>{{N}}</0> Conversations in List and Permanently Delete the Rest": "Garder seulement les <0>{{N}}</0> premières conversations de la liste et supprimer définitivement le reste", "Key Combination": "Combinaison de touches", "Keyboard Shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "Knowledge Base": "Base de connaissances", "Knowledge Base Debug": "Débogage de la base de connaissances", "Knowledge Base functionality is not available on Windows ARM64 due to library compatibility issues. This feature is supported on Windows x64, macOS, and Linux.": "La fonctionnalité de base de connaissances n'est pas disponible sur Windows ARM64 en raison de problèmes de compatibilité de bibliothèque. Cette fonctionnalité est prise en charge sur Windows x64, macOS et Linux.", "Language": "<PERSON><PERSON>", "Large file detected. Chunks will be loaded in batches of {{count}} to optimize performance.": "Fichier volumineux détecté. Les blocs seront chargés par lots de {{count}} pour optimiser les performances.", "Last Session": "Dernière session", "LaTeX Rendering (Requires Markdown)": "Rendu LaTeX (nécessite Markdown)", "Launch at system startup": "Démarrer automatiquement au démarrage du système", "License Activated": "Licence activée", "License expired, please check your license key": "Licence expirée, veuillez vérifier votre clé de licence", "License Expiry": "Expiration de la Licence", "License not found, please check your license key": "Licence non trouvée, veuillez vérifier votre clé de licence", "License Plan Overview": "Aperçu des Offres de Licence", "lifetime license": "licence à vie", "Light Mode": "<PERSON>", "List Files": "Lister les <PERSON>", "Load More Chunks": "Charger plus de blocs", "Loading chunks...": "Chargement des fragments...", "Loading files...": "Chargement des fichiers...", "Loading more chunks...": "Chargement de plus de fragments...", "Loading webpage...": "Chargement de la page web...", "Local (stdio)": "Local (stdio)", "Local Mode": "Mode Local", "Low": "Faible", "Make sure you have the following command installed:": "Assurez-vous d'avoir la commande suivante installée :", "Manage License and Devices": "Gérer la licence et les appareils", "Manually": "<PERSON><PERSON>", "Markdown Rendering": "Rendu Markdown", "Max Message Count in Context": "Nombre maximal de messages dans le contexte", "Max Output Tokens": "Nombre maximal de jetons de sortie", "max tokens in context": "Nombre maximal de jetons dans le contexte", "max tokens to generate": "Nombre maximal de jetons à générer", "Maybe Later": "Peut-être plus tard", "MCP server added": "Serveur MCP a<PERSON>", "MCP server for accessing arXiv papers": "Serveur MCP pour accéder aux articles arXiv", "MCP Settings": "Paramètres {{MCP}}", "Medium": "<PERSON><PERSON><PERSON>", "Mermaid Diagrams & Charts Rendering": "Rendu de diagrammes et de graphiques Mermaid", "meticulous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MIME Type": "Type MIME", "Misleading information": "Informations trompeuses", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "Les appareils mobiles ne prennent temporairement pas en charge l'analyse locale de ce type de fichier. Veuillez utiliser des fichiers texte (txt, markdown, etc.) ou utiliser <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> pour l'analyse de documents basée sur le cloud.", "model": "<PERSON><PERSON><PERSON><PERSON>", "Model": "<PERSON><PERSON><PERSON><PERSON>", "Model ID": "ID du modèle", "Model Provider": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Mo<PERSON>è<PERSON>", "Model Type": "Type de modèle", "Models": "<PERSON><PERSON><PERSON><PERSON>", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "Modifiez la créativité des réponses de l'IA ; plus la valeur est élevée, plus les réponses deviennent aléatoires et intrigantes, tandis qu'une valeur plus basse garantit une plus grande stabilité et fiabilité.", "More Images": "Plus d'images", "Move to Conversations": "<PERSON><PERSON><PERSON><PERSON> dans les conversations", "My Assistant": "Mon Assistant", "My Copilots": "<PERSON><PERSON>", "name": "Nom", "Name": "Nom", "Name is required": "Nom requis", "Natural": "Plus réaliste", "Navigate to the Next Conversation": "Accéder à la conversation suivante", "Navigate to the Next Option (in search dialog)": "Naviguer vers l'option suivante (dans la boîte de dialogue de recherche)", "Navigate to the Previous Conversation": "Accéder à la conversation précédente", "Navigate to the Previous Option (in search dialog)": "Naviguer vers l'option précédente (dans la boîte de dialogue de recherche)", "Navigate to the Specific Conversation": "Accéder à la conversation spécifique", "network error tips": "Une erreur réseau s'est produite. Veuillez vérifier l'état de votre réseau actuel et la connexion avec {{host}}.", "Network Proxy": "Proxy r<PERSON><PERSON>", "network proxy error tips": "Étant donné que vous avez configuré une adresse de proxy {{proxy}}, veuillez vérifier si le serveur proxy fonctionne correctement ou envisagez de supprimer l'adresse du proxy dans les paramètres.", "New": "Nouveau", "New Chat": "Nouvelle discussion", "New Images": "Nouvelles Images", "New knowledge base name": "Nouveau nom de base de connaissances", "New Thread": "Nouveau Sujet", "Nickname": "Surnom", "No": "Non", "No chunks available. Try converting the file to a text format before adding it to the knowledge base.": "Aucun segment disponible. Essayez de convertir le fichier au format texte avant de l'ajouter à la base de connaissances.", "No documents yet": "Aucun document pour l'instant", "No eligible models available": "Aucun modèle éligible disponible", "No files were dropped": "Aucun fichier n'a été déposé", "No Knowledge Base Yet": "Aucune base de connaissances pour l'instant", "No Limit": "Pas de limite", "No MCP servers parsed from clipboard": "Aucun serveur MCP n'a été analysé depuis le presse-papiers", "No permission to write file": "Permission refusée d'éc<PERSON><PERSON> le fichier", "No results found": "Aucun résultat trouvé", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "Aucun résultat de recherche trouvé. Veuillez utiliser un autre <OpenExtensionSettingButton>fournisseur de recherche</OpenExtensionSettingButton> ou réessayer plus tard.", "None": "Aucun", "not available in browser": "Cette fonctionnalité n'est pas disponible dans votre navigateur. Veuillez télécharger l'application de bureau pour accéder à toutes les fonctionnalités.", "Not set": "Non défini", "Nothing found...": "<PERSON><PERSON> trouvé...", "Number of Images per Reply": "Nombre d'Images par Réponse", "OCR Model": "Modèle OCR", "One-click MCP servers for Chatbox AI subscribers": "Serveurs MCP en un clic pour les abonnés Chatbox AI", "OpenAI API Compatible": "Compatible avec l'API OpenAI", "Operations": "Opérations", "optional": "facultatif", "or": "ou", "Or become a sponsor": "Ou devenez un sponsor", "Other concerns": "Autres préoccupations", "Paste long text as a file": "Coller un long texte comme un fichier", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "Coller un long texte comme un fichier pour maintenir les conversations propres et réduire l'utilisation des tokens avec le cache de prompt.", "Pause": "Pause", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, Code...", "Pending": "En attente", "Platform Not Supported": "Plateforme non prise en charge", "Please describe the content you want to report (Optional)": "Veuillez décrire le contenu que vous souhaitez signaler (optionnel)", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Veuillez vous assurer que le Service LM Studio distant peut se connecter à distance. Pour plus de détails, consultez <a>ce tutoriel</a>.", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Veuillez vous assurer que le service Ollama distant peut se connecter à distance. Pour plus de détails, consultez <a>ce tutoriel</a>.", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "Veuillez noter qu'en tant qu'outil client, Chatbox ne peut garantir la qualité du service et la confidentialité des données des fournisseurs de modèles. Si vous recherchez un service de modèle stable, fiable et respectueux de la vie privée, envisagez <a>Chatbox AI</a>.", "Please select a model": "Veuillez sélectionner un modèle", "Please test before saving": "Veuillez tester avant d'enregistrer.", "Please wait about 20 seconds": "Veuillez patienter environ 20 secondes", "pre-sale discount": "remise avant-vente", "premium": "premium", "Premium Activation": "Activation Premium", "Premium License Activated": "Licence Premium activée", "Premium License Key": "Clé de licence Premium", "Press hotkey": "Entrer un raccourci", "Preview": "<PERSON><PERSON><PERSON><PERSON>", "Privacy Policy": "Politique de confidentialité", "Processing failed": "Traitem<PERSON>", "Processing...": "Traitement en cours...", "Prompt": "Invite", "Provider Already Exists": "Fournisseur existe déjà", "Provider configuration is valid and ready to import": "La configuration du fournisseur est valide et prête à être importée", "Provider Details": "<PERSON><PERSON><PERSON> du fournisseur", "Provider not found": "Fournisseur non trouvé", "Provider unavailable": "Fournisseur indisponible", "proxy": "Proxy", "Proxy Address": "<PERSON><PERSON><PERSON> du proxy", "Purchase": "<PERSON><PERSON><PERSON>", "QR Code": "QR Code", "Query Knowledge Base": "Interroger la base de connaissances", "Quota Reset": "Réinitialisation du Quota", "quote": "Citer", "Rate Now": "Noter maintenant", "Read File Chunks": "<PERSON><PERSON> les morceaux de fi<PERSON>er", "Reading file...": "Lecture du fichier...", "Reasoning": "Raisonnement", "RedNote": "RedNote", "Refresh": "Actualiser", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "R<PERSON><PERSON><PERSON>z le volume des messages historiques envoyés à l'IA, en trouvant un équilibre harmonieux entre la profondeur de compréhension et l'efficacité des réponses.", "Remote (http/sse)": "À distance (http/sse)", "rename": "<PERSON>mmer", "Reply Again": "Répondre à nouveau", "Reply Again Below": "Répondre à nouveau ci-dessous", "report": "Signaler", "Report Content": "Contenu à signaler", "Report Content ID": "ID du contenu à signaler", "Report Type": "Type de signalement", "Rerank": "<PERSON><PERSON><PERSON><PERSON>", "Rerank Model": "Mod<PERSON><PERSON> de reclassement", "Rerank Model (optional)": "Modèle de reclassement (facultatif)", "reset": "Réinitialiser", "Reset": "Réinitialiser", "Reset All Hotkeys": "Réinitialiser tous les raccourcis clavier", "Reset to Default": "Rétablir les paramètres par défaut", "Reset to Global Settings": "Réinitialiser aux Paramètres Globaux", "Result": "Résultat", "Resume": "Reprendre", "Retrieve License": "Récupérer une licence", "Retrieves up-to-date documentation and code examples for any library.": "Récupère la documentation et les exemples de code à jour pour toute bibliothèque.", "Retry": "Pensez étape par étape.\n1. Le texte à traduire est \"Retry\".\n2. Le contexte est une interface utilisateur d'un logiciel d'IA chatbot.\n3. \"Retry\" est un bouton ou une action qui signifie \"essayer à nouveau\".\n4. La traduction la plus courante et appropriée pour \"Retry\" dans une interface utilisateur en français est \"Réessayer\".\n\nLa traduction est \"Réessayer\".Réessayer", "Roadmap": "Feuille de route", "save": "Enregistrer", "Save": "Enregistrer", "Save & Resend": "Enregistrer et Renvoyer", "Scope": "Portée", "Search": "Recherche", "Search All Conversations": "Rechercher dans toutes les conversations", "Search in Current Conversation": "Rechercher dans la conversation actuelle", "Search models": "Rechercher des modèles", "Search Provider": "Fournisseur de recherche", "Search query": "<PERSON><PERSON><PERSON><PERSON><PERSON> de recherche", "Search Term Construction Model": "Modèle de construction de terme de recherche", "Search...": "Rechercher...", "Select and configure an AI model provider": "Sélectionnez et configurez un fournisseur de modèle IA", "Select File": "Sélectionner un Fichier", "Select Knowledge Base": "Sélectionner une base de connaissances", "Select Model": "Sélectionner le Modèle", "Select the Current Option (in search dialog)": "Sélectionner l'option actuelle (dans la boîte de dialogue de recherche)", "send": "Envoyer", "Send": "Envoyer", "Send Without Generating Response": "Envoyer sans générer de réponse", "Set the maximum number of tokens for model output. Please set it within the acceptable range of the model, otherwise errors may occur.": "Définir le nombre maximal de jetons pour la sortie du modèle. Veuillez le définir dans la plage acceptable du modèle, sinon des erreurs peuvent survenir.", "Setting the avatar for Copilot": "Définir l'avatar du copilote", "settings": "paramètres", "Settings": "Paramètres", "Sexual content": "Contenu sexuel", "Share File": "<PERSON><PERSON>", "Share with Chatbox": "Partager avec Chatbox", "Show": "<PERSON><PERSON><PERSON><PERSON>", "Show all ({{x}})": "Afficher tout ({{x}})", "show first token latency": "Afficher la latence du premier token", "Show in Thread List": "Afficher dans la liste des sujets", "show message timestamp": "Afficher l'horodatage du message", "show message token count": "Afficher le nombre de jetons du message", "show message token usage": "Afficher l'utilisation des jetons du message", "show message word count": "Afficher le nombre de mots du message", "show model name": "Affiche<PERSON> le nom du modèle", "Show/Hide the Application Window": "Afficher/Masquer la fenêtre de l'application", "Show/Hide the Search Dialog": "Aff<PERSON>r/Masquer la boîte de dialogue de recherche", "Showing {{loaded}} of {{total}} chunks": "Affichage de {{loaded}} sur {{total}} blocs", "Showing first {{count}} chunks": "Affichage des premiers {{count}} fragments", "Smartest AI-Powered Services for Rapid Access": "Services alimentés par l'IA les plus intelligents pour un accès rapide", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "<PERSON><PERSON><PERSON><PERSON>, le modèle actuel {{model}} API ne prend pas en charge l'interprétation des images. Si vous avez besoin d'envoyer des images, veuillez passer à un autre modèle ou utiliser les <OpenMorePlanButton>modèles Chatbox AI</OpenMorePlanButton> recommandés.", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "<PERSON><PERSON><PERSON><PERSON>, le modèle actuel {{model}} API ne prend pas en charge l'interprétation des images. Si vous avez besoin d'envoyer des images, veuillez passer à un autre modèle.", "Spam or advertising": "Spam ou publicité", "Special thanks to the following sponsors:": "Un grand merci aux sponsors suivants :", "Specific model settings": "Paramètres de modèle spécifiques", "Specific model settings configured for this conversation": "Paramètres de modèle spécifiques configurés pour cette conversation", "Spell Check": "Vérification orthographique", "star": "<PERSON><PERSON><PERSON>", "Start a New Thread": "Démarrer un Nouveau Sujet", "Start Setup": "<PERSON><PERSON><PERSON><PERSON> la configuration", "Startup Page": "Page de d<PERSON>", "Status": "Statut", "stop generating": "Arrêter la génération", "Stream output": "Sortie en continu", "submit": "Envoyer", "Successfully uploaded {{count}} file(s)": "{{count}} fichier(s) importé(s) avec succès", "Successfully uploaded {{success}} of {{total}} file(s). {{failed}} file(s) failed.": "{{success}} fichier(s) sur {{total}} téléchargé(s) avec succès. {{failed}} fichier(s) ont échoué.", "Support for ChatBox development": "Support du développement de ChatBox", "Support jpg or png file smaller than 5MB": "Supporte les fichiers jpg ou png de moins de 5 Mo", "Supported formats": "Formats pris en charge", "Supports a variety of advanced AI models": "Prend en charge une variété de modèles IA avancés", "Survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Switch": "Changer", "system": "Système", "Tavily API Key": "Clé API Tavily", "temperature": "Température", "Temperature": "Température", "Terminal": "Terminal", "Test": "Test", "Thank you for your report": "Merci pour votre rapport", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "L'API {{model}} ne prend pas en charge les fichiers. Veuillez télécharger <LinkToHomePage>l'application de bureau</LinkToHomePage> pour le traitement local.", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "L'API {{model}} ne prend pas en charge les fichiers. Veuillez utiliser <LinkToAdvancedFileProcessing>modèles Chatbox AI</LinkToAdvancedFileProcessing> à la place, ou téléchargez <LinkToHomePage>l'application de bureau</LinkToHomePage> pour le traitement local.", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "L'API {{model}} ne prend pas en charge les liens. Veuillez télécharger <LinkToHomePage>l'application de bureau</LinkToHomePage> pour le traitement local.", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "L'API {{model}} ne prend pas en charge les liens. Veuillez utiliser <LinkToAdvancedUrlProcessing>modèles Chatbox AI</LinkToAdvancedUrlProcessing> à la place, ou téléchargez <LinkToHomePage>l'application de bureau</LinkToHomePage> pour le traitement local.", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "Le modèle {{model}} API ne prend pas en charge la compréhension des documents. Vous pouvez télécharger <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> pour l'analyse des documents locaux.", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "Le modèle {{model}} API ne prend pas en charge la compréhension des documents. Vous pouvez utiliser <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> pour l'analyse des documents en cloud ou télécharger <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> pour l'analyse des documents locaux.", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "L'API {{model}} en elle-même ne prend pas en charge l'envoi de fichiers. En raison de la complexité du traitement de fichiers localement, Chatbox ne traite que les fichiers basés sur du texte (y compris le code).", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "L'API {{model}} en elle-même ne prend pas en charge l'envoi de fichiers. En raison de la complexité du traitement de fichiers localement, Chatbox ne traite que les fichiers basés sur du texte (y compris le code). Pour des formats de fichiers supplémentaires et des capacités d'interprétation de documents améliorées, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> est recommandé.", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "Le modèle actuel {{model}} API ne prend pas en charge la navigation web. Modèles pris en charge : {{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "Le modèle actuel {{model}} API ne prend pas en charge la navigation web. Modèles pris en charge : <OpenMorePlanButton>Chatbox AI</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "Les données de cache pour le fichier n'ont pas été trouvées. Veuillez créer une nouvelle conversation ou actualiser le contexte, puis renvoyer le fichier.", "The current model {{model}} does not support sending links.": "Le modèle actuel {{model}} ne prend pas en charge l'envoi de liens.", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "Le modèle actuel {{model}} ne prend pas en charge l'envoi de liens. Modèles actuellement pris en charge : Chatbox AI.", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "La taille du fichier dépasse la limite de 50 Mo. Veuillez réduire la taille du fichier et réessayer.", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "Le fichier que vous avez envoyé a expiré. Pour protéger votre vie privée, toutes les données de cache liées aux fichiers ont été effacées. Vous devez créer une nouvelle conversation ou actualiser le contexte, puis renvoyer le fichier.", "The Image Creator plugin has been activated for the current conversation": "Le plugin Image Creator a été activé pour la conversation actuelle", "The license key you entered is invalid. Please check your license key and try again.": "La clé de licence que vous avez saisie est invalide. Veuillez vérifier votre clé de licence et réessayer.", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "Le paramètre topP contrôle la diversité des réponses de l'AI : les valeurs inférieures rendent la sortie plus ciblée et prévisible, tandis que les valeurs supérieures permettent des réponses plus variées et créatives.", "Theme": "Thème", "Thinking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Thinking Budget": "Budget de réflexion", "Thinking Budget only works for 2.0 or later models": "Le budget de réflexion ne fonctionne que pour les modèles 2.0 ou ultérieurs", "Thinking Budget only works for 3.7 or later models": "Le Budget de réflexion ne fonctionne que pour les modèles 3.7 ou ultérieurs", "Thinking Effort": "<PERSON><PERSON><PERSON> de r<PERSON>fle<PERSON>", "Thinking Effort only works for OpenAI o-series models": "L'Effort de réflexion ne fonctionne que pour les modèles OpenAI de la série o.", "This action cannot be undone. All documents and their embeddings will be permanently deleted.": "Cette action est irréversible. Tous les documents et leurs intégrations seront supprimés définitivement.", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "Cette clé de licence a atteint la limite d'activation, <a>cliquez ici</a> pour gérer la licence et les appareils pour désactiver les anciens appareils.", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "Ce serveur permet aux LLMs de récupérer et de traiter le contenu des pages web, en convertissant le HTML en markdown pour une consommation plus facile.", "Thread History": "Historique des Sujets", "To access locally deployed model services, please install the Chatbox desktop version": "Pour accéder aux services de modèles déployés localement, veuillez installer la version de bureau de Chatbox", "Toggle": "Basculer", "token": "<PERSON><PERSON>", "Tool use": "Utilisation des outils", "Tool Use": "Utilisation des outils", "Tools": "Outils", "Top P": "Top P", "Total Chunks": "Nombre total de morceaux", "Type": "Taper", "Type a command or search": "<PERSON><PERSON>z une commande ou recherchez", "Type your question here...": "Tapez votre question ici...", "Unknown": "Inconnu", "unknown error tips": "Erreur inconnue. Veuillez vérifier vos paramètres d'IA et l'état de votre compte, ou <0>cliquez ici pour consulter le document FAQ</0>.", "Unlock Copilot Avatar by Upgrading to Premium Edition": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'avatar du copilote en passant à l'édition Premium", "Unsaved settings": "Paramètres non enregistrés", "unstar": "Retirer l'étoile", "Untitled": "Sans titre", "Update Available": "Mise à jour disponible", "Upload failed: {{error}}": "Échec de l'envoi : {{error}}", "Upload Image": "Télécharger une image", "Upload your first document to get started": "Téléchargez votre premier document pour commencer", "Upon import, changes will take effect immediately and existing data will be overwritten": "A l'importation, les modifications prendront effet immédiatement et les données existantes seront remplacées", "Use My Own API Key / Local Model": "Utiliser ma propre clé API / Modèle local", "Use proxy to resolve CORS and other network issues": "Utiliser un proxy pour résoudre les problèmes de CORS et d'autres problèmes de réseau", "Used to extract text feature vectors, add in Settings - Provider - Model List": "Utilisé pour extraire des vecteurs de caractéristiques textuelles, ajouter dans Paramètres - Fournisseur - Liste des modèles", "Used to get more accurate search results": "Permet d'obtenir des résultats de recherche plus précis", "Used to preprocess image files, requires models with vision capabilities enabled": "Utilisé pour prétraiter les fichiers image, nécessite des modèles avec des capacités de vision activées", "user": "Utilisa<PERSON>ur", "User Avatar": "Avatar de l'utilisateur", "User Terms": "Conditions d'utilisation", "version": "Version", "View All Copilots": "Voir tous les copilotes", "View historical threads": "Voir les sujets historiques", "View More Plans": "Voir plus de plans", "Violence or dangerous content": "Contenu violent ou dangereux", "Vision": "Vision", "Vision capability is not enabled for Model {{model}}. Please enable it or set a default OCR model in <OpenSettingButton>Settings</OpenSettingButton>": "La capacité de vision n'est pas activée pour le modèle {{model}}. Veuillez l'activer ou définir un modèle OCR par défaut dans <OpenSettingButton>Paramètres</OpenSettingButton>", "Vision Model": "Mod<PERSON>le de vision", "Vision Model (optional)": "<PERSON><PERSON><PERSON><PERSON> de vision (facultatif)", "Vision, Drawing, File Understanding and more": "Vision, Dessin, Compréhension de fichiers et plus encore", "Vivid": "Plus artistique", "Web Browsing": "Navigation web", "Web browsing (coming soon)": "Navigation Web (bientôt disponible)", "Web Browsing...": "Navigation web...", "Web Search": "Recherche Internet", "WeChat": "WeChat", "What can I help you with today?": "Comment puis-je vous aider aujou<PERSON>'hui?", "Yes": "O<PERSON>", "You are already a Premium user": "Vous êtes déjà un utilisateur Premium", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "Vous avez dépassé la limite de taux pour le service Chatbox AI. Veuillez réessayer plus tard.", "You have no more Chatbox AI quota left this month.": "Vous n'avez plus de quota Chatbox AI ce mois-ci.", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "Vous avez atteint votre quota mensuel pour le modèle {{model}}. Veuillez <OpenSettingButton>aller dans les paramètres</OpenSettingButton> pour passer à un autre modèle, consulter votre utilisation de quota ou mettre à niveau votre plan.", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "Vous avez sélectionné Chatbox AI comme fournisseur de modèle, mais aucune clé de licence n'a encore été saisie. Veuillez <OpenSettingButton>cliquer ici pour ouvrir les paramètres</OpenSettingButton> et saisir votre clé de licence, ou choisir un autre fournisseur de modèle.", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "Vous avez sélectionné Chatbox AI comme fournisseur de recherche, mais aucune clé de licence n'a encore été saisie. Veuillez <OpenSettingButton>cliquer ici pour ouvrir les paramètres</OpenSettingButton> et saisir votre clé de licence, ou choisir un autre <OpenExtensionSettingButton>fournisseur de recherche</OpenExtensionSettingButton>.", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "Vous avez sélectionné <PERSON>ly comme fournisseur de recherche, mais aucune clé API n'a encore été saisie. Veuillez <OpenExtensionSettingButton>cliquer ici pour ouvrir les paramètres</OpenExtensionSettingButton> et saisir votre clé API, ou choisir un autre fournisseur de recherche.", "You have unsaved settings. Are you sure you want to leave?": "Vous avez des paramètres non enregistrés. Êtes-vous sûr de vouloir quitter?", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "Votre abonnement ChatboxAI inclut déjà accès à des modèles de différents fournisseurs. Il n'est pas nécessaire de changer de fournisseur - vous pouvez sélectionner différents modèles directement dans ChatboxAI. Passer de ChatboxAI à d'autres fournisseurs nécessitera leurs API keys respectifs. <button>Retour à ChatboxAI</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "Votre licence actuelle (Chatbox AI Lite) ne prend pas en charge le modèle {{model}}. Pour utiliser ce modèle, veuillez <OpenMorePlanButton>mettre à niveau</OpenMorePlanButton> vers Chatbox AI Pro ou un forfait de niveau supérieur. Vous pouvez également passer à un autre modèle en <OpenSettingButton>accédant aux paramètres</OpenSettingButton>.", "Your license has expired. Please check your subscription or purchase a new one.": "Votre licence a expiré. Veuillez vérifier votre abonnement ou en acheter un nouveau.", "Your rating on the App Store would help make Chatbox even better!": "Votre note sur l'App Store aidera à rendre Chatbox encore meilleur!"}