import DeepSeek from 'src/shared/models/deepseek'
import { type ModelProvider, ModelProviderEnum, type ProviderSettings, type SessionType } from 'src/shared/types'
import { createModelDependencies } from '@/adapters'
import BaseConfig from './base-config'
import type { ModelSettingUtil } from './interface'

export default class DeepSeekSettingUtil extends BaseConfig implements ModelSettingUtil {
  public provider: ModelProvider = ModelProviderEnum.DeepSeek
  async getCurrentModelDisplayName(
    model: string,
    sessionType: SessionType,
    providerSettings?: ProviderSettings
  ): Promise<string> {
    return `DeepSeek API (${providerSettings?.models?.find((m) => m.modelId === model)?.nickname || model})`
  }

  protected async listProviderModels(settings: ProviderSettings) {
    const dependencies = await createModelDependencies()
    const deepSeek = new DeepSeek(
      {
        apiKey: settings.apiKey!,
        model: {
          modelId: '',
          capabilities: [],
        },
      },
      dependencies
    )
    return deepSeek.listModels()
  }
}
