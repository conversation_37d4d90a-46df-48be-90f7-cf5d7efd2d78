<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' *.example.com"
    /> -->
    <meta
      name="viewport"
      content="height=device-height, width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="chatbox" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="chatbox" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Chatbox</title>
    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-B365F44W6E"
    ></script>
    <script
      defer
      data-domain="app.chatboxai.app"
      src="https://plausible.midway.run/js/script.local.hash.js"
    ></script>
    <script>
      window.plausible =
        window.plausible ||
        function () {
          (window.plausible.q = window.plausible.q || []).push(arguments);
        };
    </script>

    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      // gtag('config', 'G-B365F44W6E');
    </script>
    <script>
      var initialTheme = localStorage.getItem("initial-theme");
      if (initialTheme === "light" || initialTheme === "dark") {
        document.documentElement.setAttribute("data-theme", initialTheme);
        document.documentElement.setAttribute(
          "data-mantine-color-scheme",
          initialTheme
        );
      }
    </script>
    <style>
      html[data-theme="dark"] {
        background-color: #242424;
      }

      .splash-screen {
        position: fixed;
        z-index: 99999;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 1;
        background-color: #fff;
        overflow: hidden;
      }
      html[data-theme="dark"] .splash-screen {
        background-color: #242424;
      }

      .splash-screen-top {
        flex: 2;
      }
      .splash-screen-bottom {
        flex: 3;
        width: 100%;
        position: relative;
      }
      .splash-screen-content {
        flex: 0 0 auto;
        position: relative;
      }

      .splash-screen-logo {
        display: block;
        color: #495057;
      }
      html[data-theme="dark"] .splash-screen-logo {
        color: #ced4da;
      }

      .splash-screen-logo-bg {
        position: absolute;
        z-index: -1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #dee2e6;
      }
      html[data-theme="dark"] .splash-screen-logo-bg {
        color: #495057;
      }

      .splash-screen-fade-out {
        animation: fade-out 0.5s ease-in-out forwards;
      }

      @keyframes fade-out {
        from {
          opacity: 1;
        }
        to {
          opacity: 0;
        }
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <div class="splash-screen">
      <div class="splash-screen-top"></div>
      <div class="splash-screen-content">
        <svg
          class="splash-screen-logo"
          width="132"
          height="96"
          viewBox="0 0 132 96"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask
            id="path-1-outside-1_11100_16759"
            maskUnits="userSpaceOnUse"
            x="35.0715"
            y="0"
            width="62"
            height="60"
            fill="black"
          >
            <rect fill="white" x="35.0715" width="62" height="60" />
            <path
              d="M83.0247 4C88.4948 4.00025 92.929 8.43512 92.929 13.9053V38.1172C92.9287 43.5872 88.4946 48.0212 83.0247 48.0215H53.1057L43.468 56.001V46.3486C40.8172 44.5713 39.0717 41.5485 39.0715 38.1172V13.9053C39.0715 8.43496 43.5065 4 48.9768 4H83.0247Z"
            />
          </mask>
          <path
            d="M83.0247 4L83.0248 0.148105H83.0247V4ZM92.929 38.1172L96.7808 38.1173V38.1172H92.929ZM83.0247 48.0215V51.8734H83.0248L83.0247 48.0215ZM53.1057 48.0215V44.1696C52.2088 44.1696 51.3401 44.4826 50.6492 45.0545L53.1057 48.0215ZM43.468 56.001H39.6161C39.6161 57.4934 40.4782 58.8514 41.8287 59.4866C43.1791 60.1218 44.775 59.9197 45.9245 58.9679L43.468 56.001ZM43.468 46.3486H47.3199C47.3199 45.0643 46.6798 43.8645 45.6131 43.1493L43.468 46.3486ZM39.0715 38.1172H35.2196V38.1173L39.0715 38.1172ZM83.0247 4L83.0245 7.8519C86.3671 7.85205 89.0771 10.5621 89.0771 13.9053H92.929H96.7808C96.7808 6.30809 90.6225 0.148449 83.0248 0.148105L83.0247 4ZM92.929 13.9053H89.0771V38.1172H92.929H96.7808V13.9053H92.929ZM92.929 38.1172L89.0771 38.117C89.0769 41.4598 86.3673 44.1694 83.0245 44.1696L83.0247 48.0215L83.0248 51.8734C90.622 51.873 96.7805 45.7146 96.7808 38.1173L92.929 38.1172ZM83.0247 48.0215V44.1696H53.1057V48.0215V51.8734H83.0247V48.0215ZM53.1057 48.0215L50.6492 45.0545L41.0115 53.034L43.468 56.001L45.9245 58.9679L55.5622 50.9884L53.1057 48.0215ZM43.468 56.001H47.3199V46.3486H43.468H39.6161V56.001H43.468ZM43.468 46.3486L45.6131 43.1493C43.9827 42.0562 42.9235 40.2094 42.9234 38.117L39.0715 38.1172L35.2196 38.1173C35.2198 42.8875 37.6516 47.0864 41.3229 49.548L43.468 46.3486ZM39.0715 38.1172H42.9234V13.9053H39.0715H35.2196V38.1172H39.0715ZM39.0715 13.9053H42.9234C42.9234 10.5623 45.6338 7.8519 48.9768 7.8519V4V0.148105C41.3792 0.148105 35.2196 6.30762 35.2196 13.9053H39.0715ZM48.9768 4V7.8519H83.0247V4V0.148105H48.9768V4Z"
            fill="currentColor"
            mask="url(#path-1-outside-1_11100_16759)"
          />
          <circle
            cx="57.5052"
            cy="25.7339"
            r="3.02649"
            fill="currentColor"
            stroke="currentColor"
            stroke-width="0.550271"
          />
          <circle
            cx="74.5641"
            cy="25.7339"
            r="3.02649"
            fill="currentColor"
            stroke="currentColor"
            stroke-width="0.550271"
          />
          <path
            d="M33.7157 81.6562H29.9828C29.9146 81.1733 29.7754 80.7443 29.5652 80.3693C29.3549 79.9886 29.0851 79.6648 28.7555 79.3977C28.426 79.1307 28.0453 78.9261 27.6135 78.7841C27.1873 78.642 26.7243 78.571 26.2243 78.571C25.3209 78.571 24.5339 78.7955 23.8635 79.2443C23.193 79.6875 22.6731 80.3352 22.3038 81.1875C21.9345 82.0341 21.7498 83.0625 21.7498 84.2727C21.7498 85.517 21.9345 86.5625 22.3038 87.4091C22.6788 88.2557 23.2015 88.8949 23.872 89.3267C24.5424 89.7585 25.318 89.9744 26.1987 89.9744C26.693 89.9744 27.1504 89.9091 27.5709 89.7784C27.997 89.6477 28.3748 89.4574 28.7044 89.2074C29.0339 88.9517 29.3066 88.642 29.5226 88.2784C29.7441 87.9148 29.8976 87.5 29.9828 87.0341L33.7157 87.0511C33.6191 87.8523 33.3777 88.625 32.9913 89.3693C32.6106 90.108 32.0964 90.7699 31.4487 91.3551C30.8066 91.9347 30.0396 92.3949 29.1476 92.7358C28.2612 93.071 27.2584 93.2386 26.139 93.2386C24.5822 93.2386 23.1902 92.8864 21.9629 92.1818C20.7413 91.4773 19.7754 90.4574 19.0652 89.1222C18.3606 87.7869 18.0084 86.1705 18.0084 84.2727C18.0084 82.3693 18.3663 80.75 19.0822 79.4148C19.7981 78.0795 20.7697 77.0625 21.997 76.3636C23.2243 75.6591 24.6049 75.3068 26.139 75.3068C27.1504 75.3068 28.0879 75.4489 28.9515 75.733C29.8209 76.017 30.5907 76.4318 31.2612 76.9773C31.9316 77.517 32.4771 78.179 32.8976 78.9631C33.3237 79.7472 33.5964 80.6449 33.7157 81.6562ZM39.6845 85.4318V93H36.0539V75.5455H39.5823V82.2188H39.7357C40.0311 81.446 40.5084 80.8409 41.1675 80.4034C41.8266 79.9602 42.6533 79.7386 43.6476 79.7386C44.5567 79.7386 45.3493 79.9375 46.0255 80.3352C46.7073 80.7273 47.2357 81.2926 47.6107 82.0312C47.9914 82.7642 48.1789 83.642 48.1732 84.6648V93H44.5425V85.3125C44.5482 84.5057 44.3436 83.8778 43.9289 83.429C43.5198 82.9801 42.9459 82.7557 42.2073 82.7557C41.713 82.7557 41.2755 82.8608 40.8948 83.071C40.5198 83.2812 40.2243 83.5881 40.0084 83.9915C39.7982 84.3892 39.6902 84.8693 39.6845 85.4318ZM54.5234 93.2472C53.6882 93.2472 52.9438 93.1023 52.2904 92.8125C51.637 92.517 51.12 92.0824 50.7393 91.5085C50.3643 90.929 50.1768 90.2074 50.1768 89.3438C50.1768 88.6165 50.3103 88.0057 50.5774 87.5114C50.8444 87.017 51.208 86.6193 51.6683 86.3182C52.1285 86.017 52.6512 85.7898 53.2365 85.6364C53.8274 85.483 54.4467 85.375 55.0944 85.3125C55.8558 85.233 56.4694 85.1591 56.9353 85.0909C57.4012 85.017 57.7393 84.9091 57.9495 84.767C58.1597 84.625 58.2649 84.4148 58.2649 84.1364V84.0852C58.2649 83.5455 58.0944 83.1278 57.7535 82.8324C57.4183 82.5369 56.941 82.3892 56.3217 82.3892C55.6683 82.3892 55.1484 82.5341 54.762 82.8239C54.3757 83.108 54.12 83.4659 53.995 83.8977L50.637 83.625C50.8075 82.8295 51.1427 82.142 51.6427 81.5625C52.1427 80.9773 52.7876 80.5284 53.5774 80.2159C54.3728 79.8977 55.2933 79.7386 56.3387 79.7386C57.066 79.7386 57.762 79.8239 58.4268 79.9943C59.0972 80.1648 59.691 80.429 60.208 80.7869C60.7308 81.1449 61.1427 81.6051 61.4438 82.1676C61.745 82.7244 61.8955 83.392 61.8955 84.1705V93H58.4524V91.1847H58.3501C58.1399 91.5938 57.8586 91.9545 57.5063 92.267C57.1541 92.5739 56.7308 92.8153 56.2365 92.9915C55.7421 93.1619 55.1711 93.2472 54.5234 93.2472ZM55.5632 90.7415C56.0972 90.7415 56.5688 90.6364 56.9779 90.4261C57.387 90.2102 57.708 89.9205 57.941 89.5568C58.174 89.1932 58.2904 88.7812 58.2904 88.321V86.9318C58.1768 87.0057 58.0205 87.0739 57.8217 87.1364C57.6285 87.1932 57.4097 87.2472 57.1654 87.2983C56.9211 87.3437 56.6768 87.3864 56.4325 87.4261C56.1882 87.4602 55.9666 87.4915 55.7677 87.5199C55.3416 87.5824 54.9694 87.6818 54.6512 87.8182C54.333 87.9545 54.0859 88.1392 53.9097 88.3722C53.7336 88.5994 53.6455 88.8835 53.6455 89.2244C53.6455 89.7188 53.8245 90.0966 54.1825 90.358C54.5461 90.6136 55.0063 90.7415 55.5632 90.7415ZM71.4354 79.9091V82.6364H63.5518V79.9091H71.4354ZM65.3416 76.7727H68.9723V88.9773C68.9723 89.3125 69.0234 89.5739 69.1257 89.7614C69.228 89.9432 69.37 90.071 69.5518 90.1449C69.7393 90.2187 69.9553 90.2557 70.1996 90.2557C70.37 90.2557 70.5405 90.2415 70.7109 90.2131C70.8814 90.179 71.0121 90.1534 71.103 90.1364L71.674 92.8381C71.4922 92.8949 71.2365 92.9602 70.907 93.0341C70.5774 93.1136 70.1768 93.1619 69.7053 93.179C68.8303 93.2131 68.0632 93.0966 67.4041 92.8295C66.7507 92.5625 66.2422 92.1477 65.8786 91.5852C65.5149 91.0227 65.3359 90.3125 65.3416 89.4545V76.7727ZM73.9099 93V75.5455H77.5405V82.108H77.6513C77.8104 81.7557 78.0405 81.3977 78.3417 81.0341C78.6485 80.6648 79.0462 80.358 79.5349 80.1136C80.0292 79.8636 80.6428 79.7386 81.3758 79.7386C82.3303 79.7386 83.211 79.9886 84.0178 80.4886C84.8246 80.983 85.4695 81.7301 85.9525 82.7301C86.4354 83.7244 86.6769 84.9716 86.6769 86.4716C86.6769 87.9318 86.4411 89.1648 85.9695 90.1705C85.5036 91.1705 84.8672 91.929 84.0604 92.446C83.2593 92.9574 82.3616 93.2131 81.3672 93.2131C80.6627 93.2131 80.0633 93.0966 79.569 92.8636C79.0803 92.6307 78.6797 92.3381 78.3672 91.9858C78.0547 91.6278 77.8161 91.267 77.6513 90.9034H77.4894V93H73.9099ZM77.4638 86.4545C77.4638 87.233 77.5718 87.9119 77.7877 88.4915C78.0036 89.071 78.3161 89.5227 78.7252 89.8466C79.1343 90.1648 79.6315 90.3239 80.2167 90.3239C80.8076 90.3239 81.3076 90.1619 81.7167 89.8381C82.1258 89.5085 82.4354 89.054 82.6457 88.4744C82.8616 87.8892 82.9695 87.2159 82.9695 86.4545C82.9695 85.6989 82.8644 85.0341 82.6542 84.4602C82.444 83.8864 82.1343 83.4375 81.7252 83.1136C81.3161 82.7898 80.8133 82.6278 80.2167 82.6278C79.6258 82.6278 79.1258 82.7841 78.7167 83.0966C78.3133 83.4091 78.0036 83.8523 77.7877 84.4261C77.5718 85 77.4638 85.6761 77.4638 86.4545ZM94.7743 93.2557C93.4504 93.2557 92.3055 92.9744 91.3396 92.4119C90.3794 91.8437 89.6379 91.054 89.1152 90.0426C88.5924 89.0256 88.3311 87.8466 88.3311 86.5057C88.3311 85.1534 88.5924 83.9716 89.1152 82.9602C89.6379 81.9432 90.3794 81.1534 91.3396 80.5909C92.3055 80.0227 93.4504 79.7386 94.7743 79.7386C96.0981 79.7386 97.2402 80.0227 98.2004 80.5909C99.1663 81.1534 99.9106 81.9432 100.433 82.9602C100.956 83.9716 101.217 85.1534 101.217 86.5057C101.217 87.8466 100.956 89.0256 100.433 90.0426C99.9106 91.054 99.1663 91.8437 98.2004 92.4119C97.2402 92.9744 96.0981 93.2557 94.7743 93.2557ZM94.7913 90.4432C95.3936 90.4432 95.8964 90.2727 96.2998 89.9318C96.7032 89.5852 97.0072 89.1136 97.2118 88.517C97.422 87.9205 97.5271 87.2415 97.5271 86.4801C97.5271 85.7188 97.422 85.0398 97.2118 84.4432C97.0072 83.8466 96.7032 83.375 96.2998 83.0284C95.8964 82.6818 95.3936 82.5085 94.7913 82.5085C94.1834 82.5085 93.672 82.6818 93.2572 83.0284C92.8481 83.375 92.5385 83.8466 92.3282 84.4432C92.1237 85.0398 92.0214 85.7188 92.0214 86.4801C92.0214 87.2415 92.1237 87.9205 92.3282 88.517C92.5385 89.1136 92.8481 89.5852 93.2572 89.9318C93.672 90.2727 94.1834 90.4432 94.7913 90.4432ZM105.904 79.9091L108.307 84.4858L110.77 79.9091H114.494L110.702 86.4545L114.597 93H110.889L108.307 88.4744L105.767 93H102.017L105.904 86.4545L102.154 79.9091H105.904Z"
            fill="currentColor"
          />
        </svg>

        <svg
          class="splash-screen-logo-bg"
          width="660"
          height="660"
          viewBox="0 0 660 660"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            opacity="0.6"
            cx="331"
            cy="330"
            r="229.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.5"
            cx="330"
            cy="330"
            r="254.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.8"
            cx="330"
            cy="330"
            r="179.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.9"
            cx="331"
            cy="330"
            r="154.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            cx="330"
            cy="330"
            r="129.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.7"
            cx="331"
            cy="330"
            r="204.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.4"
            cx="331"
            cy="330"
            r="279.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.3"
            cx="330"
            cy="330"
            r="304.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
          <circle
            opacity="0.2"
            cx="330"
            cy="330"
            r="329.75"
            stroke="currentColor"
            stroke-width="0.5"
          />
        </svg>
      </div>
      <div class="splash-screen-bottom" id="log-root"></div>
    </div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
